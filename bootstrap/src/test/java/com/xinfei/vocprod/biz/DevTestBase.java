/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz;

import com.xinfei.vocprod.bootstrap.Application;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * 单系统集成测试基类
 *
 * <AUTHOR>
 * @version $ TechplayDevTestBase, v 0.1 2023/8/29 21:28 <PERSON><PERSON>.Huang Exp $
 */
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@ExtendWith(SpringExtension.class)
@ActiveProfiles({"local","test"})
@AutoConfigureMockMvc
@RunWith(MockitoJUnitRunner.class)
public abstract class DevTestBase {

}
