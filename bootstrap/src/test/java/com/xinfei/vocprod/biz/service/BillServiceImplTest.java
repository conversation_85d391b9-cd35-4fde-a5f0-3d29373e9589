package com.xinfei.vocprod.biz.service;

import com.xinfei.vocprod.biz.DevTestBase;
import com.xinfei.vocprod.biz.api.rr.BillingSubmitRequest;
import com.xinfei.vocprod.biz.service.impl.BillServiceImpl;
import com.xinfei.vocprod.itl.impl.WorkOrderFeignClientImpl;
import com.xinfei.vocprod.itl.rr.CreateTaskDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


public class BillServiceImplTest extends DevTestBase {

    @InjectMocks
    private BillServiceImpl billService;

    @Mock
    private WorkOrderFeignClientImpl workOrderFeignClient;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 设置配置属性
        ReflectionTestUtils.setField(billService, "taskType", 100);
        ReflectionTestUtils.setField(billService, "subTaskType", 200);
    }

    /**
     * 测试开票申请提交
     */
    @Test
    public void testBillingSubmit() {
        // 准备测试数据
        BillingSubmitRequest request = new BillingSubmitRequest();
        request.setName("张三");
        request.setMobile("13800138000");
        request.setIdCardNumber("110101199001011234");
        request.setEmail("<EMAIL>");
        request.setAppeal("测试开票申请");

        // 模拟依赖行为
        doNothing().when(workOrderFeignClient).createTask(any(CreateTaskDto.class));

        // 执行测试
        Boolean result = billService.billingSubmit(request);

        // 验证结果
        assertTrue(result);
        
        // 验证工单创建参数
        ArgumentCaptor<CreateTaskDto> taskCaptor = ArgumentCaptor.forClass(CreateTaskDto.class);
        verify(workOrderFeignClient, times(1)).createTask(taskCaptor.capture());
        
        CreateTaskDto capturedTask = taskCaptor.getValue();
        assertEquals(true, capturedTask.getIsCustomize());
        assertEquals(Integer.valueOf(100), capturedTask.getCustomizeSceneId());
        assertEquals(Integer.valueOf(200), capturedTask.getCustomizeChannel());
        assertEquals(Integer.valueOf(200), capturedTask.getTaskTypeId());
        assertEquals(Integer.valueOf(0), capturedTask.getQuestionTypeId());
        assertEquals(Integer.valueOf(1), capturedTask.getEmergencyStatus());
        assertEquals("110101199001011234", capturedTask.getRefundAmount());
        assertEquals("xyf01", capturedTask.getAppId());
        assertEquals("13312341234", capturedTask.getCallNumber());
        assertEquals("13800138000", capturedTask.getDevice());
        assertEquals("张三", capturedTask.getVipOrderNumber());
        assertEquals("邮箱：<EMAIL> 具体诉求：测试开票申请", capturedTask.getComment());
    }

    /**
     * 测试开票申请提交 - 空字段
     */
    @Test
    public void testBillingSubmit_EmptyFields() {
        // 准备测试数据
        BillingSubmitRequest request = new BillingSubmitRequest();
        request.setName("");
        request.setMobile("");
        request.setIdCardNumber("");
        request.setEmail("");
        request.setAppeal("");

        // 模拟依赖行为
        doNothing().when(workOrderFeignClient).createTask(any(CreateTaskDto.class));

        // 执行测试
        Boolean result = billService.billingSubmit(request);

        // 验证结果
        assertTrue(result);
        
        // 验证工单创建参数
        ArgumentCaptor<CreateTaskDto> taskCaptor = ArgumentCaptor.forClass(CreateTaskDto.class);
        verify(workOrderFeignClient, times(1)).createTask(taskCaptor.capture());
        
        CreateTaskDto capturedTask = taskCaptor.getValue();
        assertEquals("", capturedTask.getRefundAmount());
        assertEquals("", capturedTask.getDevice());
        assertEquals("", capturedTask.getVipOrderNumber());
        assertEquals("邮箱： 具体诉求：", capturedTask.getComment());
    }

    /**
     * 测试开票申请提交 - null字段
     */
    @Test
    public void testBillingSubmit_NullFields() {
        // 准备测试数据
        BillingSubmitRequest request = new BillingSubmitRequest();
        // 所有字段默认为null

        // 模拟依赖行为
        doNothing().when(workOrderFeignClient).createTask(any(CreateTaskDto.class));

        // 执行测试
        Boolean result = billService.billingSubmit(request);

        // 验证结果
        assertTrue(result);
        
        // 验证工单创建参数
        ArgumentCaptor<CreateTaskDto> taskCaptor = ArgumentCaptor.forClass(CreateTaskDto.class);
        verify(workOrderFeignClient, times(1)).createTask(taskCaptor.capture());
        
        CreateTaskDto capturedTask = taskCaptor.getValue();
        assertEquals(null, capturedTask.getRefundAmount());
        assertEquals(null, capturedTask.getDevice());
        assertEquals(null, capturedTask.getVipOrderNumber());
        assertEquals("邮箱：null 具体诉求：null", capturedTask.getComment());
    }
}
