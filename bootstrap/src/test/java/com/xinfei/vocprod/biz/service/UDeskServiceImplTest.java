package com.xinfei.vocprod.biz.service;

import com.xinfei.vocprod.biz.DevTestBase;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskLinkRequest;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskResultDto;
import com.xinfei.vocprod.biz.config.VocProdApolloConfig;
import com.xinfei.vocprod.biz.enums.AppException;
import com.xinfei.vocprod.biz.model.entity.RequestUser;
import com.xinfei.vocprod.biz.model.entity.UdeskConfBo;
import com.xinfei.vocprod.biz.service.common.CisCommonService;
import com.xinfei.vocprod.biz.service.impl.UDeskServiceImpl;
import com.xinfei.vocprod.facade.rr.UDeskRequest;
import com.xinfei.vocprod.facade.rr.dto.Customer;
import com.xinfei.vocprod.itl.client.UDeskClient;
import com.xinfei.vocprod.itl.impl.CisFacadeClientImpl;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class UDeskServiceImplTest extends DevTestBase {

    @InjectMocks
    private UDeskServiceImpl uDeskService;

    @Mock
    private UDeskClient uDeskClient;

    @Mock
    private VocProdApolloConfig vocProdApolloConfig;

    @Mock
    private CisCommonService cisCommonService;

    @Mock
    private CisFacadeClientImpl cisFacadeClient;

    private String mockUdeskConfig;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // 模拟配置
        mockUdeskConfig = "{\"app1\":{\"url\":\"https://example.com/app1\",\"im_user_key\":\"app1_key\",\"public_key\":\"app1_public_key\"},\"app2\":{\"url\":\"https://example.com/app2\",\"im_user_key\":\"app2_key\",\"public_key\":\"app2_public_key\"}}";
        ReflectionTestUtils.setField(uDeskService, "newUDeskConfig", mockUdeskConfig);

        // 注意：由于不使用PowerMock，我们需要使用其他方式模拟JsonUtil
    }

    /**
     * 测试获取RSA URL
     */
    @Test
    public void testGetRsaUrl() {
        // 准备测试数据
        UDeskRequest request = new UDeskRequest();
        Customer customer = new Customer();
        customer.setWeb_token("123456");
        customer.setC_cf_uid("123456");
        customer.setC_cf_dialogueDesc("123456");
        request.setCustomer(customer);
        request.setUrlBase("https://example.com");
        request.setUpbKey("public_key");
        request.setImUserKey("im_key");

        // 模拟依赖行为
        when(vocProdApolloConfig.isEncfUid()).thenReturn(true);
        when(vocProdApolloConfig.isCustNewEnabled()).thenReturn(false);
        when(uDeskClient.getRsaUrl(any(UDeskRequest.class), eq(true))).thenReturn("https://example.com/rsa_url");

        // 执行测试
        String result = uDeskService.getRsaUrl(request);

        // 验证结果
        assertEquals("https://example.com/rsa_url", result);
    }

    /**
     * 测试获取RSA URL - 当custNewEnabled为true时
     */
    @Test
    public void testGetRsaUrl_WithCustNewEnabled() {
        // 准备测试数据
        UDeskRequest request = new UDeskRequest();
        Customer customer = new Customer();
        customer.setWeb_token("123456");
        customer.setC_cf_uid("123456");
        customer.setC_cf_dialogueDesc("123456");
        request.setCustomer(customer);
        request.setUrlBase("https://example.com");
        request.setUpbKey("public_key");
        request.setImUserKey("im_key");

        // 模拟CIS用户查询结果
        UserSearchDTO currentUserCisInfo = new UserSearchDTO();
        currentUserCisInfo.setMobile("13800138000");
        currentUserCisInfo.setApp("xyf");
        currentUserCisInfo.setUserNo(123456L);

        List<UserSearchDTO> currentUserList = new ArrayList<>();
        currentUserList.add(currentUserCisInfo);
        PageResult<UserSearchDTO> currentUserCisResult = new PageResult<>();
        currentUserCisResult.setList(currentUserList);

        // 模拟相关用户查询结果
        UserSearchDTO relatedUser1 = new UserSearchDTO();
        relatedUser1.setApp("xyf");
        relatedUser1.setUserNo(123456L);

        UserSearchDTO relatedUser2 = new UserSearchDTO();
        relatedUser2.setApp("xyf01");
        relatedUser2.setUserNo(654321L);

        List<UserSearchDTO> relatedUserList = new ArrayList<>();
        relatedUserList.add(relatedUser1);
        relatedUserList.add(relatedUser2);
        PageResult<UserSearchDTO> relatedCisUsersResult = new PageResult<>();
        relatedCisUsersResult.setList(relatedUserList);

        // 模拟依赖行为
        when(vocProdApolloConfig.isEncfUid()).thenReturn(true);
        when(vocProdApolloConfig.isCustNewEnabled()).thenReturn(true);

        // 注入cisFacadeClient到uDeskClient
        ReflectionTestUtils.setField(uDeskClient, "cisFacadeClient", cisFacadeClient);

        // 模拟cisFacadeClient的行为
        when(cisFacadeClient.queryUserList(isNull(), isNull(), eq("123456"), eq(1), eq(1))).thenReturn(currentUserCisResult);
        when(cisFacadeClient.queryUserList(eq("13800138000"), isNull(), isNull(), eq(1), eq(10))).thenReturn(relatedCisUsersResult);

        // 模拟最终的URL生成
        when(uDeskClient.getRsaUrl(any(UDeskRequest.class), eq(true))).thenAnswer(invocation -> {
            UDeskRequest req = invocation.getArgument(0);
            // 验证用户信息是否被正确更新
            assertEquals("654321", req.getCustomer().getWeb_token());
            assertEquals("654321", req.getCustomer().getC_cf_uid());
            assertEquals("654321", req.getCustomer().getC_cf_dialogueDesc());
            return "https://example.com/rsa_url_updated";
        });

        // 执行测试
        String result = uDeskService.getRsaUrl(request);

        // 验证结果
        assertEquals("https://example.com/rsa_url_updated", result);

        // 验证cisFacadeClient方法被调用
        verify(cisFacadeClient).queryUserList(isNull(), isNull(), eq("123456"), eq(1), eq(1));
        verify(cisFacadeClient).queryUserList(eq("13800138000"), isNull(), isNull(), eq(1), eq(10));
    }

    /**
     * 测试获取Udesk链接
     */
    @Test
    public void testGetUdeskLink() {
        // 准备测试数据
        HelpCenterGetUdeskLinkRequest request = new HelpCenterGetUdeskLinkRequest();
        request.setUserNo("123456");
        request.setApp("app1");

        RequestUser requestUser = new RequestUser();
        requestUser.setUserNo(123456L);
        requestUser.setName("测试用户");
        requestUser.setMobile("13800138000");

        Map<String, UdeskConfBo> confBoMap = new HashMap<>();
        UdeskConfBo udeskConfBo = new UdeskConfBo();
        udeskConfBo.setUrl("https://example.com/app1");
        udeskConfBo.setImUserKey("app1_key");
        udeskConfBo.setPublicKey("app1_public_key");
        confBoMap.put("app1", udeskConfBo);

        // 模拟依赖行为
        when(cisCommonService.getRequestUserByUserNo(123456L)).thenReturn(requestUser);
        // 使用ReflectionTestUtils注入confBoMap，而不是模拟静态方法
        ReflectionTestUtils.setField(uDeskService, "udeskConfBoMap", confBoMap);
        when(uDeskClient.getRsaUrl(any(UDeskRequest.class), eq(true))).thenReturn("https://example.com/rsa_url");
        when(vocProdApolloConfig.isEncfUid()).thenReturn(true);
        when(vocProdApolloConfig.isCustNewEnabled()).thenReturn(false);

        // 执行测试
        HelpCenterGetUdeskResultDto result = uDeskService.getUdeskLink(request);

        // 验证结果
        assertNotNull(result);
        assertEquals("https://example.com/rsa_url", result.getUrl());
    }

    /**
     * 测试获取Udesk链接 - 用户不存在
     */
    @Test(expected = AppException.class)
    public void testGetUdeskLink_UserNotExist() {
        // 准备测试数据
        HelpCenterGetUdeskLinkRequest request = new HelpCenterGetUdeskLinkRequest();
        request.setUserNo("123456");
        request.setApp("app1");

        // 模拟依赖行为
        when(cisCommonService.getRequestUserByUserNo(123456L)).thenReturn(null);

        // 执行测试 - 应该抛出异常
        uDeskService.getUdeskLink(request);
    }

    /**
     * 测试获取Udesk链接 - 应用配置不存在
     */
    @Test(expected = AppException.class)
    public void testGetUdeskLink_AppConfigNotExist() {
        // 准备测试数据
        HelpCenterGetUdeskLinkRequest request = new HelpCenterGetUdeskLinkRequest();
        request.setUserNo("123456");
        request.setApp("app_not_exist");

        RequestUser requestUser = new RequestUser();
        requestUser.setUserNo(123456L);

        Map<String, UdeskConfBo> confBoMap = new HashMap<>();
        UdeskConfBo udeskConfBo = new UdeskConfBo();
        udeskConfBo.setUrl("https://example.com/app1");
        udeskConfBo.setImUserKey("app1_key");
        udeskConfBo.setPublicKey("app1_public_key");
        confBoMap.put("app1", udeskConfBo);

        // 模拟依赖行为
        when(cisCommonService.getRequestUserByUserNo(123456L)).thenReturn(requestUser);
        // 使用ReflectionTestUtils注入confBoMap，而不是模拟静态方法
        ReflectionTestUtils.setField(uDeskService, "udeskConfBoMap", confBoMap);

        // 执行测试 - 应该抛出异常
        uDeskService.getUdeskLink(request);
    }

    /**
     * 测试获取Udesk链接 - JSON解析异常
     */
    @Test(expected = AppException.class)
    public void testGetUdeskLink_JsonParseException() {
        // 准备测试数据
        HelpCenterGetUdeskLinkRequest request = new HelpCenterGetUdeskLinkRequest();
        request.setUserNo("123456");
        request.setApp("app1");

        RequestUser requestUser = new RequestUser();
        requestUser.setUserNo(123456L);

        // 模拟依赖行为
        when(cisCommonService.getRequestUserByUserNo(123456L)).thenReturn(requestUser);
        // 模拟JSON解析异常
        ReflectionTestUtils.setField(uDeskService, "udeskConfBoMap", null);
        // 设置一个无效的JSON字符串，强制解析失败
        ReflectionTestUtils.setField(uDeskService, "newUDeskConfig", "{\"无效JSON\"");

        // 执行测试 - 应该抛出异常
        uDeskService.getUdeskLink(request);
    }
}
