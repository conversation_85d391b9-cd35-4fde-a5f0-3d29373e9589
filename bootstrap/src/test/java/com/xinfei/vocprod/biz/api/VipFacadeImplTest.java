package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.DevTestBase;
import com.xinfei.vocprod.biz.api.rr.VipInfoResponse;
import com.xinfei.vocprod.biz.api.rr.VipOperation;
import com.xinfei.vocprod.biz.api.rr.VipRequest;
import com.xinfei.vocprod.biz.service.VipService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;


public class VipFacadeImplTest extends DevTestBase {

    @InjectMocks
    private VipFacadeImpl vipFacade;

    @Mock
    private VipService vipService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试获取VIP操作 - 正常场景
     */
    @Test
    public void testGetVipOperation_Normal() {
        // 准备测试数据
        VipRequest request = new VipRequest();
        request.setUserNo("123456");

        VipOperation vipOperation = new VipOperation();
        vipOperation.setCanRefund(true);
        vipOperation.setCanSelfRefund(false);
        vipOperation.setCanSelfCancelWithhold(true);
        vipOperation.setCanSelfCloseRenew(true);

        // 模拟依赖行为
        when(vipService.getVipInfo("123456")).thenReturn(vipOperation);

        // 执行测试
        VipInfoResponse response = vipFacade.getVipOperation(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuc());
        assertNotNull(response.getVipOperation());
        assertEquals(true, response.getVipOperation().getCanRefund());
        assertEquals(false, response.getVipOperation().getCanSelfRefund());
        assertEquals(true, response.getVipOperation().getCanSelfCancelWithhold());
        assertEquals(true, response.getVipOperation().getCanSelfCloseRenew());
    }

    /**
     * 测试获取VIP操作 - 空结果
     */
    @Test
    public void testGetVipOperation_EmptyResult() {
        // 准备测试数据
        VipRequest request = new VipRequest();
        request.setUserNo("123456");

        // 模拟依赖行为
        when(vipService.getVipInfo("123456")).thenReturn(null);

        // 执行测试
        VipInfoResponse response = vipFacade.getVipOperation(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuc());
        assertNull(response.getVipOperation());
    }

    /**
     * 测试获取VIP操作 - 服务层返回空对象
     */
    @Test
    public void testGetVipOperation_EmptyObject() {
        // 准备测试数据
        VipRequest request = new VipRequest();
        request.setUserNo("123456");

        // 模拟依赖行为 - 返回空对象
        when(vipService.getVipInfo("123456")).thenReturn(new VipOperation());

        // 执行测试
        VipInfoResponse response = vipFacade.getVipOperation(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuc());
        assertNotNull(response.getVipOperation());
        assertNull(response.getVipOperation().getCanRefund());
    }

    /**
     * 测试获取VIP操作 - 服务层抛出异常
     */
    @Test
    public void testGetVipOperation_ServiceThrowException() {
        // 准备测试数据
        VipRequest request = new VipRequest();
        request.setUserNo("123456");

        // 模拟依赖行为
        when(vipService.getVipInfo("123456")).thenThrow(new RuntimeException("服务异常"));

        // 执行测试
        VipInfoResponse response = vipFacade.getVipOperation(request);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试获取VIP操作 - 请求参数为null
     */
    @Test
    public void testGetVipOperation_NullRequest() {
        // 执行测试
        VipInfoResponse response = vipFacade.getVipOperation(null);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试获取VIP操作 - 请求参数缺少必要字段
     */
    @Test
    public void testGetVipOperation_MissingRequiredFields() {
        // 准备测试数据 - 缺少userNo
        VipRequest request = new VipRequest();

        // 执行测试
        VipInfoResponse response = vipFacade.getVipOperation(request);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }
}
