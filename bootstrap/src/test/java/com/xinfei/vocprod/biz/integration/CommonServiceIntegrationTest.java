package com.xinfei.vocprod.biz.integration;

import com.xinfei.vocprod.biz.DevTestBase;
import com.xinfei.vocprod.biz.common.RandomBizKey;
import com.xinfei.vocprod.biz.service.CommonService;
import com.xinfei.vocprod.facade.rr.GetABRequest;
import com.xinfei.vocprod.itl.impl.RandomGeneratorClientImpl;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;


public class CommonServiceIntegrationTest extends DevTestBase {

    @Autowired
    private CommonService commonService;

    @MockBean
    private RandomGeneratorClientImpl randomGeneratorClient;

    /**
     * 测试正常场景 - 获取AB分组
     */
    @Test
    public void testGetGroupAB_Integration() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("123456789");
        request.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 模拟依赖行为
        when(randomGeneratorClient.ab(eq("123456789"), eq(RandomBizKey.KFDT_HYJZX))).thenReturn("groupA");

        // 执行测试
        String result = commonService.getGroupAB(request);

        // 验证结果
        assertEquals("groupA", result);
    }

    /**
     * 测试场景 - 不同用户获取不同分组
     */
    @Test
    public void testGetGroupAB_DifferentUsers() {
        // 准备测试数据 - 用户1
        GetABRequest request1 = new GetABRequest();
        request1.setUserNo("123456789");
        request1.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 准备测试数据 - 用户2
        GetABRequest request2 = new GetABRequest();
        request2.setUserNo("987654321");
        request2.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 模拟依赖行为
        when(randomGeneratorClient.ab(eq("123456789"), anyString())).thenReturn("groupA");
        when(randomGeneratorClient.ab(eq("987654321"), anyString())).thenReturn("groupB");

        // 执行测试
        String result1 = commonService.getGroupAB(request1);
        String result2 = commonService.getGroupAB(request2);

        // 验证结果
        assertEquals("groupA", result1);
        assertEquals("groupB", result2);
    }

    /**
     * 测试场景 - 不同业务键获取不同分组
     */
    @Test
    public void testGetGroupAB_DifferentBizKeys() {
        // 准备测试数据 - 业务键1
        GetABRequest request1 = new GetABRequest();
        request1.setUserNo("123456789");
        request1.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 准备测试数据 - 业务键2
        GetABRequest request2 = new GetABRequest();
        request2.setUserNo("123456789");
        request2.setBizKey(RandomBizKey.KFDT_CNXW_DMX);

        // 模拟依赖行为
        when(randomGeneratorClient.ab(anyString(), eq(RandomBizKey.KFDT_HYJZX))).thenReturn("groupA");
        when(randomGeneratorClient.ab(anyString(), eq(RandomBizKey.KFDT_CNXW_DMX))).thenReturn("groupB");

        // 执行测试
        String result1 = commonService.getGroupAB(request1);
        String result2 = commonService.getGroupAB(request2);

        // 验证结果
        assertEquals("groupA", result1);
        assertEquals("groupB", result2);
    }
}
