package com.xinfei.vocprod.biz.service;

import com.xinfei.psenginecore.facade.rr.request.VocKeywordsRequest;
import com.xinfei.vocprod.biz.DevTestBase;
import com.xinfei.vocprod.biz.config.VocProdApolloConfig;
import com.xinfei.vocprod.biz.service.impl.PsengineServiceImpl;
import com.xinfei.vocprod.facade.request.VocKeyRequest;
import com.xinfei.vocprod.facade.response.VocKeyResponse;
import com.xinfei.vocprod.itl.CashiercoreFeignClient;
import com.xinfei.vocprod.itl.PsengineFeinClient;
import com.xinfei.vocprod.itl.impl.NPayServiceImpl;
import com.xinfei.vocprod.itl.rr.PublicAccountInfo;
import com.xinfei.vocprod.itl.rr.PublicAccountRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * PsengineServiceImpl单元测试
 *
 * <AUTHOR>
 * @version $ PsengineServiceImplTest, v 0.1 2024-12-20 14:03 AI Exp $
 */
public class PsengineServiceImplTest extends DevTestBase {

    @InjectMocks
    private PsengineServiceImpl psengineService;

    @Mock
    private PsengineFeinClient psengineFeinClient;

    @Mock
    private CashiercoreFeignClient cashiercoreFeignClient;

    @Mock
    private VocProdApolloConfig vocProdApolloConfig;

    @Mock
    private NPayServiceImpl nPayService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试vocKeywordsSet方法 - 正常场景
     */
    @Test
    public void testVocKeywordsSet_Normal() {
        // 准备测试数据
        VocKeyRequest request = new VocKeyRequest();
        VocKeyRequest.CustomerExtra customerExtra = new VocKeyRequest.CustomerExtra();
        customerExtra.setDialogueDesc("*********");
        request.setCustomerExtra(customerExtra);

        // 模拟依赖行为
        when(psengineFeinClient.vocKeywordsSet(any(VocKeywordsRequest.class))).thenReturn(true);
        when(vocProdApolloConfig.isCashOpenFlag()).thenReturn(false);

        // 执行测试
        VocKeyResponse response = psengineService.vocKeywordsSet(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(Integer.valueOf(0), response.getResCode());
        assertEquals("Success", response.getResError());
        assertNotNull(response.getMessage());
        verify(psengineFeinClient, times(1)).vocKeywordsSet(any(VocKeywordsRequest.class));
        verify(cashiercoreFeignClient, never()).vocKeywordsSet(any());
    }

    /**
     * 测试vocKeywordsSet方法 - 保存失败场景
     */
    @Test
    public void testVocKeywordsSet_SaveFail() {
        // 准备测试数据
        VocKeyRequest request = new VocKeyRequest();
        VocKeyRequest.CustomerExtra customerExtra = new VocKeyRequest.CustomerExtra();
        customerExtra.setDialogueDesc("*********");
        request.setCustomerExtra(customerExtra);

        // 模拟依赖行为
        when(psengineFeinClient.vocKeywordsSet(any(VocKeywordsRequest.class))).thenReturn(false);
        when(vocProdApolloConfig.isCashOpenFlag()).thenReturn(false);

        // 执行测试
        VocKeyResponse response = psengineService.vocKeywordsSet(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(Integer.valueOf(-1), response.getResCode());
        assertEquals("Save fail", response.getResError());
        assertNotNull(response.getMessage());
        verify(psengineFeinClient, times(1)).vocKeywordsSet(any(VocKeywordsRequest.class));
    }

    /**
     * 测试vocKeywordsSet方法 - 参数校验失败场景
     */
    @Test
    public void testVocKeywordsSet_InvalidParam() {
        // 准备测试数据 - 空请求
        VocKeyRequest request = new VocKeyRequest();

        // 执行测试
        VocKeyResponse response = psengineService.vocKeywordsSet(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(Integer.valueOf(-1), response.getResCode());
        assertEquals("The mandatory parameter is null", response.getResError());
        verify(psengineFeinClient, never()).vocKeywordsSet(any(VocKeywordsRequest.class));
    }

    /**
     * 测试queryPublicAccountInfo方法 - 正常场景
     */
    @Test
    public void testQueryPublicAccountInfo_Normal() {
        // 准备测试数据
        VocKeyRequest request = new VocKeyRequest();
        VocKeyRequest.CustomerExtra customerExtra = new VocKeyRequest.CustomerExtra();
        customerExtra.setDialogueDesc("*********");
        request.setCustomerExtra(customerExtra);

        // 模拟依赖行为
        PublicAccountInfo accountInfo = new PublicAccountInfo();
        accountInfo.setBankCardNo("6225*********012");
        accountInfo.setBankAccountName("测试用户");
        accountInfo.setBankName("测试银行");
        accountInfo.setOpenCityName("北京市");
        accountInfo.setOpenBankBranch("测试支行");
        accountInfo.setOpenBankNo("123456");
        when(nPayService.queryPublicAccountInfo(any(PublicAccountRequest.class))).thenReturn(accountInfo);

        // 执行测试
        VocKeyResponse response = psengineService.queryPublicAccountInfo(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(Integer.valueOf(0), response.getResCode());
        assertEquals("Success", response.getResError());
        assertNotNull(response.getMessage());
        assertNotNull(response.getVariables());

        Map<String, Object> variables = response.getVariables();
        assertEquals("6225*********012", variables.get("bankCardNo"));
        assertEquals("测试用户", variables.get("bankAccountName"));
        assertEquals("测试银行", variables.get("bankName"));
        assertEquals("北京市", variables.get("openCityName"));
        assertEquals("测试支行", variables.get("openBankBranch"));
        assertEquals("123456", variables.get("openBankNo"));

        verify(nPayService, times(1)).queryPublicAccountInfo(any(PublicAccountRequest.class));
    }

    /**
     * 测试queryPublicAccountInfo方法 - 账户信息不存在场景
     */
    @Test
    public void testQueryPublicAccountInfo_NoAccountInfo() {
        // 准备测试数据
        VocKeyRequest request = new VocKeyRequest();
        VocKeyRequest.CustomerExtra customerExtra = new VocKeyRequest.CustomerExtra();
        customerExtra.setDialogueDesc("*********");
        request.setCustomerExtra(customerExtra);

        // 模拟依赖行为
        when(nPayService.queryPublicAccountInfo(any(PublicAccountRequest.class))).thenReturn(null);

        // 执行测试
        VocKeyResponse response = psengineService.queryPublicAccountInfo(request);

        // 验证结果
        assertNotNull(response);
        assertEquals(Integer.valueOf(-1), response.getResCode());
        assertEquals("No account info found", response.getResError());
        assertNotNull(response.getMessage());
        verify(nPayService, times(1)).queryPublicAccountInfo(any(PublicAccountRequest.class));
    }
}
