package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.DevTestBase;
import com.xinfei.vocprod.biz.api.rr.BillingSubmitRequest;
import com.xinfei.vocprod.biz.api.rr.BillingSubmitResponse;
import com.xinfei.vocprod.biz.service.impl.BillServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


public class BillFacadeImplTest extends DevTestBase {

    @InjectMocks
    private BillFacadeImpl billFacade;

    @Mock
    private BillServiceImpl billService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试开票申请提交 - 正常场景
     */
    @Test
    public void testBillSubmit_Normal() {
        // 准备测试数据
        BillingSubmitRequest request = new BillingSubmitRequest();
        request.setName("张三");
        request.setMobile("13800138000");
        request.setIdCardNumber("110101199001011234");
        request.setEmail("<EMAIL>");
        request.setAppeal("测试开票申请");

        // 模拟依赖行为
        when(billService.billingSubmit(any(BillingSubmitRequest.class))).thenReturn(true);

        // 执行测试
        BillingSubmitResponse response = billFacade.billSubmit(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuc());
        assertTrue(response.getResult());
    }

    /**
     * 测试开票申请提交 - 服务层返回失败
     */
    @Test
    public void testBillSubmit_ServiceReturnFalse() {
        // 准备测试数据
        BillingSubmitRequest request = new BillingSubmitRequest();
        request.setName("张三");
        request.setMobile("13800138000");
        request.setIdCardNumber("110101199001011234");
        request.setEmail("<EMAIL>");
        request.setAppeal("测试开票申请");

        // 模拟依赖行为
        when(billService.billingSubmit(any(BillingSubmitRequest.class))).thenReturn(false);

        // 执行测试
        BillingSubmitResponse response = billFacade.billSubmit(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuc());
        assertFalse(response.getResult());
    }

    /**
     * 测试开票申请提交 - 服务层抛出异常
     */
    @Test
    public void testBillSubmit_ServiceThrowException() {
        // 准备测试数据
        BillingSubmitRequest request = new BillingSubmitRequest();
        request.setName("张三");
        request.setMobile("13800138000");
        request.setIdCardNumber("110101199001011234");
        request.setEmail("<EMAIL>");
        request.setAppeal("测试开票申请");

        // 模拟依赖行为
        when(billService.billingSubmit(any(BillingSubmitRequest.class))).thenThrow(new RuntimeException("服务异常"));

        // 执行测试
        BillingSubmitResponse response = billFacade.billSubmit(request);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试开票申请提交 - 请求参数为null
     */
    @Test
    public void testBillSubmit_NullRequest() {
        // 执行测试
        BillingSubmitResponse response = billFacade.billSubmit(null);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }
}
