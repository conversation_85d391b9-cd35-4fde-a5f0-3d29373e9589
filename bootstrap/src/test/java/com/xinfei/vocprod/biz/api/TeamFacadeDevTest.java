/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.DevTestBase;
import com.xinfei.vocprod.facade.TeamFacade;
import com.xinfei.vocprod.facade.rr.TeamQueryRequest;
import com.xinfei.vocprod.facade.rr.TeamQueryResponse;
import org.junit.Assert;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TeamFacade研发自测
 *
 * <AUTHOR>
 * @version $ TeamFacadeDevTest, v 0.1 2023/8/29 09:30 Jinyan.Huang Exp $
 */
public class TeamFacadeDevTest extends DevTestBase {

    @Autowired
    private TeamFacade teamFacade;

    @BeforeEach
    public void beforeEach() {
        //System.out.println("beforeEach");
    }

    @AfterEach
    public void afterEach() {
        //System.out.println("afterEach");
    }

    /**
     * TeamFacade正常场景用列
     *
     * <p>用列说明：
     * <li>Case-N-01：根据TeamCode参数正常查询到1条数据
     *
     * @throws Exception 测试执行异常，无需捕获
     */
    @Test
    public void testQueryByCodeSuc() throws Exception {

        // 1 构造请求
        TeamQueryRequest request = new TeamQueryRequest();
        request.setTeamCode("test");

        // 2 执行请求
        TeamQueryResponse response = teamFacade.queryByCode(request);

        // 3 结果校验
        Assert.assertTrue("返回结果状态验证失败", response.isSuc());
        Assert.assertEquals("团队名称验证失败", "信飞技术中心", response.getData().getTeamName());
    }

    /**
     * TeamFacade异常场景用列
     *
     * <p>用列说明：
     * <li>Case-E-01：TeamCode为空
     *
     * @throws Exception 测试执行异常，无需捕获
     */
    @Test
    public void testQueryByCodeFail() throws Exception {

        // 1 构造请求
        TeamQueryRequest request = new TeamQueryRequest();

        // 2 执行请求
        TeamQueryResponse response = teamFacade.queryByCode(request);

        // 3 结果校验
        Assert.assertFalse("返回结果状态验证失败", response.isSuc());
        Assert.assertEquals("错误码验证失败", "XE0100003101", response.getErrorContext().getErrCode());
    }
}
