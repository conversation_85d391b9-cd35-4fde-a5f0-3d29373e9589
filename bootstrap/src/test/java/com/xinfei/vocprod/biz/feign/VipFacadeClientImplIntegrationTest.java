/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.feign;

/**
 * <AUTHOR>
 * @version $ VipFacadeClientImplIntegrationTest, v 0.1 2025-04-24 16:22 junjie.yan Exp $
 */


import com.xinfei.vipcore.facade.rr.response.ShowRefundEntryDTO;
import com.xinfei.vocprod.biz.DevTestBase;
import com.xinfei.vocprod.biz.service.VipService;
import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.impl.VipFacadeClientImpl;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.fail;


@Slf4j
class VipFacadeClientImplIntegrationTest extends DevTestBase {

    @Resource
    private VipFacadeClientImpl vipFacadeClient;

    @Resource
    private VipService vipService;

    @BeforeEach
    public void setUp() {
        // 确保 TekRefundFacade 已正确注入（依赖 Spring 上下文）
    }

    @Test
    void testShowRefundEntry_Success() {
        // 前置条件：确保 TekRefundFacade 的远程服务可用
        Long validUserNo = 111111115822020L; // 替换为实际存在的用户编号

        try {
            ShowRefundEntryDTO result = vipFacadeClient.showRefundEntry(validUserNo);
            assertNotNull(result.getShowEntry());
            // 验证返回对象的字段（如 showEntry、vocJumpUrl 等）
            // 例如：assertTrue(result.getShowEntry());
        } catch (ClientException e) {
            fail("Expected success but got exception: " + e.getMessage());
        }
    }


    @Test
    public void test() {
        vipService.isSuperVip("1939303089100716176");
    }

    @Test
    public void testShowRefundEntry_Exception() {
        // 模拟无效的 userNo，触发远程服务返回错误
        Long invalidUserNo = -1L;

        assertThrows(ClientException.class, () -> {
            vipFacadeClient.showRefundEntry(invalidUserNo);
        });
    }
}
