///**
// * Xinfei.com Inc.
// * Copyright (c) 2004-2023 All Rights Reserved.
// */
//package com.xinfei.vocprod.biz.api;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.xinfei.vocprod.facade.UserFacade;
//import com.xinfei.vocprod.facade.rr.UserRequest;
//import com.xinfei.vocprod.facade.rr.UserResponse;
//import com.xinfei.vocprod.facade.rr.dto.UserDto;
//import org.junit.Assert;
//import org.junit.jupiter.api.AfterEach;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
///**
// * UserFacade研发自测
// *
// * <AUTHOR>
// * @version $ TeamFacadeDevTest, v 0.1 2023/8/29 09:30 <PERSON><PERSON>.Huang Exp $
// */
//public class UserFacadeDevTest extends TeamFacadeDevTest {
//    UserDto user = new UserDto();
//    @Autowired
//    private UserMapper userMapper;
//    @Autowired
//    private UserFacade userFacade;
//
//    @BeforeEach
//    public void beforeEach() {
//        user = new UserDto();
//        user.setAge(123);
//        user.setName("tefsdfsfsfsfstt");
//        user.setEmail("<EMAIL>");
//        clear();
//    }
//
//    private void clear() {
//        LambdaQueryWrapper<UserEntity> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(UserEntity::getName, user.getName());
//        userMapper.delete(queryWrapper);
//    }
//
//    @AfterEach
//    public void afterEach() {
//        clear();
//    }
//
//    @Test
//    public void testAddSuc() throws Exception {
//        // 1 构造请求
//        UserRequest request = new UserRequest();
//        request.setUser(user);
//
//        // 2 执行请求
//        UserResponse response = userFacade.addOrEdit(request);
//
//        //3 结果校验
//        LambdaQueryWrapper<UserEntity> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(UserEntity::getName, user.getName());
//        UserEntity user1 = userMapper.selectOne(queryWrapper);
//        assert (user1.getEmail().equalsIgnoreCase(user.getEmail()));
//    }
//
//    @Test
//    public void testEditSuc() throws Exception {
//        // 1 构造请求
//        UserRequest request = new UserRequest();
//        request.setUser(user);
//
//        // 2 执行请求
//        UserResponse response = userFacade.addOrEdit(request);
//
//
//        //.andExpect(jsonPath("$.id").isNumber().value(1));
//        LambdaQueryWrapper<UserEntity> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(UserEntity::getName, user.getName());
//        UserEntity user1 = userMapper.selectOne(queryWrapper);
//        user.setName("ttttttttt");
//        user.setId(user1.getId());
//        request.setUser(user);
//
//
//        response = userFacade.addOrEdit(request);
//
//        queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(UserEntity::getName, user.getName());
//        Long count = userMapper.selectCount(queryWrapper);
//
//        //结果校验
//        Assert.assertTrue("用户更新失败", count == 1);
//        user1 = userMapper.selectOne(queryWrapper);
//        Assert.assertTrue("用户更新失败", user.getName().equalsIgnoreCase(user1.getName()));
//
//
//    }
//
//    @Test
//    public void testUserFail() throws Exception {
//        // 1 构造请求
//        UserRequest request = new UserRequest();
//
//        // 2 执行请求
//        UserResponse response = userFacade.addOrEdit(request);
//
//        // 3 校验结果
//        Assert.assertFalse("返回结果状态验证失败", response.isSuc());
//        Assert.assertEquals("user参数验证失败", "Techplay交易类场景/服务调用请求信息不合法/用户:user不允许为null 异常原因：服务调用请求信息不合法|用户:user不允许为null", response.getErrorContext().getErrDesc());
//        Assert.assertEquals("错误码验证失败", "XE0100002101", response.getErrorContext().getErrCode());
//    }
//
//    @Test
//    public void testUserNameFail() throws Exception {
//        // 1 构造请求
//        UserRequest request = new UserRequest();
//        UserDto userDto = new UserDto();
//        userDto.setAge(342);
//        request.setUser(userDto);
//
//        // 2 执行请求
//        UserResponse response = userFacade.addOrEdit(request);
//
//        // 3 校验结果
//        Assert.assertFalse("返回结果状态验证失败", response.isSuc());
//        Assert.assertEquals("user name参数验证失败", "Techplay交易类场景/服务调用请求信息不合法/用户名称:name不允许为空 异常原因：服务调用请求信息不合法|用户名称:name不允许为空", response.getErrorContext().getErrDesc());
//        Assert.assertEquals("错误码验证失败", "XE0100002101", response.getErrorContext().getErrCode());
//    }
//
//    @Test
//    public void testUserEmailFail() throws Exception {
//        // 1 构造请求
//        UserRequest request = new UserRequest();
//        UserDto userDto = new UserDto();
//        userDto.setAge(342);
//        userDto.setName("test");
//        request.setUser(userDto);
//
//        // 2 执行请求
//        UserResponse response = userFacade.addOrEdit(request);
//
//        // 3 校验结果
//        Assert.assertFalse("返回结果状态验证失败", response.isSuc());
//        Assert.assertEquals("user email参数验证失败", "Techplay交易类场景/服务调用请求信息不合法/用户email:email不允许为空 异常原因：服务调用请求信息不合法|用户email:email不允许为空", response.getErrorContext().getErrDesc());
//        Assert.assertEquals("错误码验证失败", "XE0100002101", response.getErrorContext().getErrCode());
//    }
//}
