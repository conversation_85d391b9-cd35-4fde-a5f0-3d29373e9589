package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.DevTestBase;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskLinkRequest;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskResultDto;
import com.xinfei.vocprod.biz.enums.AppErrDtlEnum;
import com.xinfei.vocprod.biz.enums.AppException;
import com.xinfei.vocprod.biz.service.UDeskService;
import com.xinfei.vocprod.facade.rr.UDeskRequest;
import com.xinfei.vocprod.facade.rr.UDeskResponse;
import com.xinfei.vocprod.facade.rr.dto.Customer;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


public class UDeskFacadeImplTest extends DevTestBase {

    @InjectMocks
    private UDeskFacadeImpl uDeskFacade;

    @Mock
    private UDeskService uDeskService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试获取RSA URL - 正常场景
     */
    @Test
    public void testGetRsaUrl_Normal() {
        // 准备测试数据
        UDeskRequest request = new UDeskRequest();
        Customer customer = new Customer();
        customer.setWeb_token("123456");
        customer.setC_cf_uid("123456");
        customer.setC_cf_dialogueDesc("123456");
        request.setCustomer(customer);
        request.setUrlBase("https://example.com");
        request.setUpbKey("public_key");
        request.setImUserKey("im_key");

        // 模拟依赖行为
        when(uDeskService.getRsaUrl(any(UDeskRequest.class))).thenReturn("https://example.com/rsa_url");

        // 执行测试
        UDeskResponse response = uDeskFacade.getRsaUrl(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuc());
        assertEquals("https://example.com/rsa_url", response.getUrl());
    }

    /**
     * 测试获取RSA URL - 服务层抛出异常
     */
    @Test
    public void testGetRsaUrl_ServiceThrowException() {
        // 准备测试数据
        UDeskRequest request = new UDeskRequest();
        Customer customer = new Customer();
        customer.setWeb_token("123456");
        customer.setC_cf_uid("123456");
        customer.setC_cf_dialogueDesc("123456");
        request.setCustomer(customer);
        request.setUrlBase("https://example.com");
        request.setUpbKey("public_key");
        request.setImUserKey("im_key");

        // 模拟依赖行为
        when(uDeskService.getRsaUrl(any(UDeskRequest.class))).thenThrow(new RuntimeException("服务异常"));

        // 执行测试
        UDeskResponse response = uDeskFacade.getRsaUrl(request);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试获取RSA URL - 请求参数为null
     */
    @Test
    public void testGetRsaUrl_NullRequest() {
        // 执行测试
        UDeskResponse response = uDeskFacade.getRsaUrl(null);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试获取RSA URL - 请求参数缺少必要字段
     */
    @Test
    public void testGetRsaUrl_MissingRequiredFields() {
        // 准备测试数据 - 缺少customer
        UDeskRequest request = new UDeskRequest();
        request.setUrlBase("https://example.com");
        request.setUpbKey("public_key");
        request.setImUserKey("im_key");

        // 执行测试
        UDeskResponse response = uDeskFacade.getRsaUrl(request);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试获取新的UDesk链接 - 正常场景
     */
    @Test
    public void testGetNewUDeskLink_Normal() {
        // 准备测试数据
        HelpCenterGetUdeskLinkRequest request = new HelpCenterGetUdeskLinkRequest();
        request.setUserNo("123456");
        request.setApp("app1");

        HelpCenterGetUdeskResultDto serviceResult = new HelpCenterGetUdeskResultDto();
        serviceResult.setUrl("https://example.com/udesk_link");

        // 模拟依赖行为
        when(uDeskService.getUdeskLink(any(HelpCenterGetUdeskLinkRequest.class))).thenReturn(serviceResult);

        // 执行测试
        HelpCenterGetUdeskResultDto response = uDeskFacade.getNewUDeskLink(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuc());
        assertEquals("https://example.com/udesk_link", response.getUrl());
    }

    /**
     * 测试获取新的UDesk链接 - 服务层抛出异常
     */
    @Test
    public void testGetNewUDeskLink_ServiceThrowException() {
        // 准备测试数据
        HelpCenterGetUdeskLinkRequest request = new HelpCenterGetUdeskLinkRequest();
        request.setUserNo("123456");
        request.setApp("app1");

        // 模拟依赖行为
        when(uDeskService.getUdeskLink(any(HelpCenterGetUdeskLinkRequest.class))).thenThrow(new AppException(AppErrDtlEnum.USER_NOT_EXIST));

        // 执行测试
        HelpCenterGetUdeskResultDto response = uDeskFacade.getNewUDeskLink(request);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试获取新的UDesk链接 - 请求参数为null
     */
    @Test
    public void testGetNewUDeskLink_NullRequest() {
        // 执行测试
        HelpCenterGetUdeskResultDto response = uDeskFacade.getNewUDeskLink(null);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试获取新的UDesk链接 - 请求参数缺少必要字段
     */
    @Test
    public void testGetNewUDeskLink_MissingRequiredFields() {
        // 准备测试数据 - 缺少userNo
        HelpCenterGetUdeskLinkRequest request = new HelpCenterGetUdeskLinkRequest();
        request.setApp("app1");

        // 执行测试
        HelpCenterGetUdeskResultDto response = uDeskFacade.getNewUDeskLink(request);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }
}
