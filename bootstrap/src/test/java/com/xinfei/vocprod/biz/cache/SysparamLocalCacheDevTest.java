///**
// * Xinfei.com Inc.
// * Copyright (c) 2004-2023 All Rights Reserved.
// */
//package com.xinfei.vocprod.biz.cache;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.xinfei.vocprod.biz.TechplayDevTestBase;
//import org.junit.Assert;
//import org.junit.jupiter.api.AfterEach;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.Date;
//
///**
// * 系统参数缓存研发集成测试自测
// *
// * <AUTHOR>
// * @version $ SysparamLocalCacheDevTest, v 0.1 2023/10/8 21:26 Jinyan.huang Exp $
// */
//public class SysparamLocalCacheDevTest extends TechplayDevTestBase {
//
//    private static final String TEST_SYS_PARAM_CODE = "TEST_SYS_PARAM_CODE";
//    private static final String TEST_SYS_PARAM_VALUE = "TEST_SYS_PARAM_VALUE";
//
//    @Autowired
//    private SysparamLocalCache sysparamLocalCache;
//
//    @Autowired
//    private SysparamMapper sysparamMapper;
//
//
//    @BeforeEach
//    public void beforeEach() {
//        clear();
//        SysparamEntity sysparamEntity = new SysparamEntity();
//        sysparamEntity.setSysCode(TEST_SYS_PARAM_CODE);
//        sysparamEntity.setSysValue(TEST_SYS_PARAM_VALUE);
//        sysparamEntity.setMemo("系统参数缓存研发集成测试自测");
//        sysparamEntity.setCreatedTime(new Date());
//        sysparamEntity.setUpdatedTime(new Date());
//        sysparamMapper.insert(sysparamEntity);
//    }
//
//    @AfterEach
//    public void afterEach() {
//        clear();
//    }
//
//    private void clear() {
//        LambdaQueryWrapper<SysparamEntity> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(SysparamEntity::getSysCode, TEST_SYS_PARAM_CODE);
//        sysparamMapper.delete(queryWrapper);
//    }
//
//    @Test
//    public void testFetchSystemValue() throws Exception {
//        // 1 强制刷新缓存
//        sysparamLocalCache.refresh(true);
//
//        // 2 执行请求
//        String sysparamValue = sysparamLocalCache.fetchSystemValue(TEST_SYS_PARAM_CODE);
//
//        //3 结果校验
//        Assert.assertEquals(TEST_SYS_PARAM_VALUE, sysparamValue);
//    }
//}
