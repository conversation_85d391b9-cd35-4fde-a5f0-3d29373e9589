package com.xinfei.vocprod.biz.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xinfei.vocprod.biz.DevTestBase;
import com.xinfei.vocprod.biz.common.RandomBizKey;
import com.xinfei.vocprod.biz.service.CommonService;
import com.xinfei.vocprod.facade.rr.GetABRequest;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc
public class CommonFacadeImplMvcTest extends DevTestBase {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @MockBean
    private CommonService commonService;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    /**
     * 测试正常场景 - 获取AB分组
     */
    @Test
    public void testGetGroupAB_Normal() throws Exception {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("123456789");
        request.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 模拟依赖行为
        when(commonService.getGroupAB(any(GetABRequest.class))).thenReturn("groupA");

        // 执行测试
        mockMvc.perform(post("/common/getGroupAB")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.suc").value(true))
                .andExpect(jsonPath("$.group").value("groupA"));
    }

    /**
     * 测试参数校验 - userNo为null
     */
    @Test
    public void testGetGroupAB_NullUserNo() throws Exception {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo(null);
        request.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 执行测试
        mockMvc.perform(post("/common/getGroupAB")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.suc").value(false))
                .andExpect(jsonPath("$.errorContext").exists());
    }

    /**
     * 测试参数校验 - bizKey为null
     */
    @Test
    public void testGetGroupAB_NullBizKey() throws Exception {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("123456789");
        request.setBizKey(null);

        // 执行测试
        mockMvc.perform(post("/common/getGroupAB")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.suc").value(false))
                .andExpect(jsonPath("$.errorContext").exists());
    }

    /**
     * 测试异常场景 - CommonService抛出异常
     */
    @Test
    public void testGetGroupAB_ServiceException() throws Exception {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("123456789");
        request.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 模拟依赖行为抛出异常
        when(commonService.getGroupAB(any(GetABRequest.class))).thenThrow(new RuntimeException("模拟异常"));

        // 执行测试
        mockMvc.perform(post("/common/getGroupAB")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.suc").value(false))
                .andExpect(jsonPath("$.errorContext").exists());
    }
}
