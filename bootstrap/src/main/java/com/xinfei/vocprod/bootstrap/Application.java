package com.xinfei.vocprod.bootstrap;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication(scanBasePackages = {"com.xinfei.vocprod"})
@ServletComponentScan
@RestController
@MapperScan("com.xinfei.vocprod.dal.mapper")
@Slf4j
public class Application {

    public static void main(String[] args) {
        log.info("系统启动开始");
        try {
            // 启动spring boot
            SpringApplication.run(Application.class, args);
            // TODO 预留业务初始化 系统变量、全局配置等等
        } catch (Throwable t) {
            log.error("系统启动异常", t);
            System.exit(1);
        }
        log.info("系统启动成功!!!");
    }

}
