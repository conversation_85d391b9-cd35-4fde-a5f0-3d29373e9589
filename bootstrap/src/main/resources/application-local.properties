################ 1 app comm ################
server.port=8088
################ 2 Spring comm ################
# Bean dependence
spring.main.allow-circular-references=true

# json
spring.jackson.default-property-inclusion=non_null
################ 3 spring default datasource ################
################ 5 biz config ################
# vocprod remote service
xf.vocprod.url=http://localhost:8088
# root level
vocprod.logging.level=info
# app work dir
vocprod.logging.home=../logs/vocprod
xf.localcache.startupDelay=30
