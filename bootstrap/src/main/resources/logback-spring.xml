<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- properties -->
    <springProperty scope="context" name="vocprod.logging.home" source="vocprod.logging.home"/>
    <springProperty scope="context" name="vocprod.logging.level" source="vocprod.logging.level"  defaultValue="INFO"/>
    <springProperty scope="context" name="env" source="env"/>

    <!--    所有输出格式保持一致-->
    <property name="FILE_LOG_PATTERN"
              value="[%d] [%thread][%X{Vocprod-TraceID}][%X{traceId}] %-5level %logger{50} - %msg %n"/>
    <conversionRule conversionWord="msg"
                    converterClass="com.xinfei.vocprod.util.util.ArgumentJsonFormatLayout"/>

    <!-- 1 通用appenders -->
    <!-- 1.1 控制台appender, IDE环境使用 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <param name="target" value="System.out"/>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
        <param name="target" value="System.err"/>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 1.2.1 默认日志输出appender, 没有特殊配置的INFO级别日志都放在这个文件 -->
    <appender name="DEFAULT-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <File>${vocprod.logging.home}/common-default.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${vocprod.logging.level}</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件的滚动命名模式，按日期和索引进行滚动 -->
            <fileNamePattern>${vocprod.logging.home}/common-default.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <!-- 每个归档文件的最大大小为 100MB -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志文件保留的最大天数为 14 天 -->
            <maxHistory>14</maxHistory>
            <!-- 所有归档文件的总大小不超过 2GB，超过后会删除旧的归档文件 -->
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 1.2.2 错误日志输出appender, ERROR级别日志必须输出到该文件 -->
    <appender name="ERROR-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <File>${vocprod.logging.home}/common-error.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件的滚动命名模式，按日期和索引进行滚动 -->
            <fileNamePattern>${vocprod.logging.home}/common-error.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <!-- 每个归档文件的最大大小为 100MB -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志文件保留的最大天数为 14 天 -->
            <maxHistory>14</maxHistory>
            <!-- 所有归档文件的总大小不超过 2GB，超过后会删除旧的归档文件 -->
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 1.2.3 sls监控日志输出appender, 监控日志输出在这里 -->
    <appender name="MONITOR-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <fieldName>timestamp</fieldName>
                    <pattern>yyyy-MM-dd HH:mm:ss.SSS</pattern>
                </timestamp>
                <pattern>
                    <pattern>
                        {
                        "logKey":"%X{logKey}",
                        "uri":"%X{uri}",
                        "method":"%X{method}",
                        "topic":"%X{topic}",
                        "tag":"%X{tag}",
                        "feignName":"%X{feignName}",
                        "remoteIp":"%X{remoteIp}",
                        "costTime":"%X{costTime}",
                        "result":"%X{result}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <File>${vocprod.logging.home}/monitor.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <FileNamePattern>${vocprod.logging.home}/monitor.log.%i</FileNamePattern>
            <MinIndex>0</MinIndex>
            <MaxIndex>9</MaxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>200MB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="MONITOR-APPENDER-ORIGIN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <File>${vocprod.logging.home}/monitor-origin.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <FileNamePattern>${vocprod.logging.home}/monitor-origin.log.%i</FileNamePattern>
            <MinIndex>0</MinIndex>
            <MaxIndex>9</MaxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>200MB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <!-- 添加一个专门用于控制台的 JSON 格式 appender -->
    <appender name="STDOUT-JSON" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <fieldName>timestamp</fieldName>
                    <pattern>yyyy-MM-dd HH:mm:ss.SSS</pattern>
                </timestamp>
                <pattern>
                    <pattern>
                        {
                        "logKey":"%X{logKey}",
                        "uri":"%X{uri}",
                        "method":"%X{method}",
                        "topic":"%X{topic}",
                        "tag":"%X{tag}",
                        "feignName":"%X{feignName}",
                        "remoteIp":"%X{remoteIp}",
                        "costTime":"%X{costTime}",
                        "result":"%X{result}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!-- 2 root loggers, 分级输出日志 -->
    <springProfile name="local">
        <!-- 普通日志输出到控制台 -->
        <root level="${vocprod.logging.level}">
            <appender-ref ref="STDOUT"/>
        </root>

        <!-- monitor logger 使用 JSON 格式输出到控制台 -->
        <logger name="monitor" level="INFO" additivity="false">
            <appender-ref ref="STDOUT-JSON"/>
        </logger>
    </springProfile>

    <springProfile name="!local">
        <!-- 非本地环境，启用 monitor logger -->
        <logger name="monitor" level="INFO" additivity="false">
            <appender-ref ref="MONITOR-APPENDER"/>
        </logger>
        <logger name="monitor-origin" level="INFO" additivity="false">
            <appender-ref ref="MONITOR-APPENDER-ORIGIN"/>
        </logger>
        <root level="${vocprod.logging.level}">
            <appender-ref ref="DEFAULT-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </root>
    </springProfile>

    <!--    无自定义appenders 不需要-->
    <!-- 3 系统自定义appenders -->
    <!-- 3.1 业务处理过程日志 -->
    <!--    <appender name="BIZ-SERVICE-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
    <!--        <encoder>-->
    <!--            <pattern>[%d] [%thread][%X{traceId}] - %msg %n</pattern>-->
    <!--        </encoder>-->
    <!--        <File>${vocprod.logging.home}/biz-service.7dt.log</File>-->
    <!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
    <!--            <fileNamePattern>${vocprod.logging.home}/biz-service.7dt.log.%d{yyyy-MM-dd}</fileNamePattern>-->
    <!--            <maxHistory>7</maxHistory>-->
    <!--        </rollingPolicy>-->
    <!--    </appender>-->
    <!--    <logger name="BIZ-SERVICE" additivity="false">-->
    <!--        <level value="${vocprod.logging.level}" />-->
    <!--        <if condition='!property("env").contains("prod")&amp;&amp;!property("env").contains("pro")'>-->
    <!--            <then>-->
    <!--                &lt;!&ndash; this only enable in local &ndash;&gt;-->
    <!--                <appender-ref ref="STDOUT"/>-->
    <!--            </then>-->
    <!--        </if>-->
    <!--        <appender-ref ref="BIZ-SERVICE-APPENDER" />-->
    <!--        <appender-ref ref="ERROR-APPENDER" />-->
    <!--    </logger>-->

</configuration>
