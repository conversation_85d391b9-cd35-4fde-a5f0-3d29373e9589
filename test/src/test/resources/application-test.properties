spring.application.name=vocprod

# æ¥å¿è·¯å¾éç½®
vocprod.logging.home = logs/vocprod/



# test mode,local or remote
test.mode=local

# test flag
test.case.flag = true

spring.main.allow-bean-definition-overriding=true

# lendtrade db
#test.testdbconfig.datasource[0].data-source-name=techplayTestDataSource
#test.testdbconfig.datasource[0].jdbc-url=${spring.datasource.url}
#test.testdbconfig.datasource[0].username=${spring.datasource.username}
#test.testdbconfig.datasource[0].password=${spring.datasource.password}
#test.testdbconfig.datasource[0].driver-class-name=com.mysql.cj.jdbc.Driver

# table list for datasource, to see: test.testdbconfig.datasource[*].data-source-name
#test.testdbconfig.tablesmapping.mapping[techplayTestDataSource]=*


