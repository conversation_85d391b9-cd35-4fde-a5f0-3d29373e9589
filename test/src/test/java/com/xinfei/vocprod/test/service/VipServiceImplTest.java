package com.xinfei.vocprod.test.service;

import com.xinfei.vipcore.facade.rr.response.ShowRefundEntryDTO;
import com.xinfei.vocprod.biz.config.AppVocConfig;
import com.xinfei.vocprod.biz.service.impl.IndexServiceImpl;
import com.xinfei.vocprod.itl.impl.RandomGeneratorClientImpl;
import com.xinfei.vocprod.itl.impl.VipFacadeClientImpl;
import com.xinfei.vocprod.test.base.AdvanceDevTestBase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class VipServiceImplTest extends AdvanceDevTestBase {

    @InjectMocks
    private IndexServiceImpl indexService;

    @Mock
    private AppVocConfig appVocConfig;

    @Mock
    private RandomGeneratorClientImpl randomGeneratorClient;

    @Mock
    private VipFacadeClientImpl vipFacadeClient;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // 模拟 appVocConfig.getQuestions1() 返回值
        String questions1Json = "{ \"category\": \"猜你想问\", \"list\": [ { \"id\": 10001, \"ask\": \"如何更换手机号\" }, { \"id\": 10002, \"ask\": \"如何取消订单\" } ] }";
        when(appVocConfig.getQuestions1()).thenReturn(questions1Json);

        // 模拟 randomGeneratorClient.ab() 返回值
        when(randomGeneratorClient.ab(anyString(), anyString())).thenReturn("groupB");

        // 模拟 vipFacadeClient.showRefundEntry() 返回值
        when(vipFacadeClient.showRefundEntry(anyLong())).thenReturn(new ShowRefundEntryDTO().setShowEntry(true));
    }

    @Test
    public void testFaqConfig() throws Exception {
        // 模拟 getDynamicConfig() 返回值
//        when(vipService.getDynamicConfig()).thenReturn(Arrays.asList(10001L, 10002L));

        // 调用 faqConfig 方法
        Map<String, Object> result = indexService.faqConfig("1939303089098213330");

        // 验证结果
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> questions = (List<Map<String, Object>>) result.get("questions");

        // 验证 questions 中的第一个元素（defaultConfig）
        @SuppressWarnings("unchecked")
        Map<String, Object> defaultConfig = (Map<String, Object>) questions.get(0);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> list = (List<Map<String, Object>>) defaultConfig.get("list");

        // 验证 list 的顺序
        assertEquals("如何更换手机号", ((Map<String, Object>) list.get(0)).get("ask"));
        assertEquals("如何取消订单", ((Map<String, Object>) list.get(1)).get("ask"));

        // 验证 questions 的数量（假设其他 FAQ 也添加进来）
        assertEquals(9, questions.size()); // 默认FAQ + 其他7个FAQ

        // 其他验证可以根据具体需求进行
    }
}
