package com.xinfei.vocprod.test.facade;

import com.xinfei.vocprod.biz.api.CommonFacadeImpl;
import com.xinfei.vocprod.biz.common.RandomBizKey;
import com.xinfei.vocprod.biz.service.CommonService;
import com.xinfei.vocprod.facade.rr.GetABRequest;
import com.xinfei.vocprod.facade.rr.GetABResponse;
import com.xinfei.vocprod.test.base.AdvanceDevTestBase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


public class CommonFacadeImplTest extends AdvanceDevTestBase {

    @InjectMocks
    private CommonFacadeImpl commonFacade;

    @Mock
    private CommonService commonService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(commonFacade, "commonService", commonService);
    }

    /**
     * 测试正常场景 - 获取AB分组
     */
    @Test
    public void testGetGroupAB_Normal() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("123456789");
        request.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 模拟依赖行为
        when(commonService.getGroupAB(any(GetABRequest.class))).thenReturn("groupA");

        // 执行测试
        GetABResponse response = commonFacade.getGroupAB(request);

        // 验证结果
        assertTrue(response.isSuc());
        assertEquals("groupA", response.getGroup());
    }

    /**
     * 测试场景 - 获取AB分组返回groupB
     */
    @Test
    public void testGetGroupAB_ReturnGroupB() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("987654321");
        request.setBizKey(RandomBizKey.KFDT_CNXW_DMX);

        // 模拟依赖行为
        when(commonService.getGroupAB(any(GetABRequest.class))).thenReturn("groupB");

        // 执行测试
        GetABResponse response = commonFacade.getGroupAB(request);

        // 验证结果
        assertTrue(response.isSuc());
        assertEquals("groupB", response.getGroup());
    }

    /**
     * 测试异常场景 - CommonService抛出异常
     */
    @Test
    public void testGetGroupAB_ServiceException() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("123456789");
        request.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 模拟依赖行为抛出异常
        when(commonService.getGroupAB(any(GetABRequest.class))).thenThrow(new RuntimeException("模拟异常"));

        // 执行测试
        GetABResponse response = commonFacade.getGroupAB(request);

        // 验证结果 - 应该返回失败响应
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试参数校验 - 请求为null
     */
    @Test
    public void testGetGroupAB_NullRequest() {
        // 执行测试
        GetABResponse response = commonFacade.getGroupAB(null);

        // 验证结果 - 应该返回失败响应
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试参数校验 - userNo为null
     */
    @Test
    public void testGetGroupAB_NullUserNo() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo(null);
        request.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 执行测试
        GetABResponse response = commonFacade.getGroupAB(request);

        // 验证结果 - 应该返回失败响应
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }

    /**
     * 测试参数校验 - bizKey为null
     */
    @Test
    public void testGetGroupAB_NullBizKey() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("123456789");
        request.setBizKey(null);

        // 执行测试
        GetABResponse response = commonFacade.getGroupAB(request);

        // 验证结果 - 应该返回失败响应
        assertFalse(response.isSuc());
        assertNotNull(response.getErrorContext());
    }
}
