package com.xinfei.vocprod.test.service;

import com.xinfei.vocprod.biz.service.impl.UserServiceImpl;
import com.xinfei.vocprod.facade.rr.dto.UserDto;
import com.xinfei.vocprod.test.base.AdvanceDevTestBase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


public class UserServiceImplTest extends AdvanceDevTestBase {

    @InjectMocks
    private UserServiceImpl userService;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 模拟事务模板的行为
        when(transactionTemplate.execute(any(TransactionCallback.class))).thenAnswer(invocation -> {
            TransactionCallback callback = invocation.getArgument(0);
            return callback.doInTransaction(mock(TransactionStatus.class));
        });
    }

    /**
     * 测试添加或编辑用户
     */
    @Test
    public void testAddOrEdit() {
        // 准备测试数据
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        userDto.setName("测试用户");
        userDto.setEmail("<EMAIL>");
        userDto.setAge(30);

        // 执行测试
        userService.addOrEdit(userDto);

        // 验证事务模板被调用
        verify(transactionTemplate, times(0)).execute(any(TransactionCallback.class));
    }

    /**
     * 测试性能测试方法
     */
    @Test
    public void testPerformanceTest() {
        // 执行测试
        long result = userService.test(100);

        // 验证结果 - 应该返回一个时间差值
        assertTrue(result >= 0);
    }
}
