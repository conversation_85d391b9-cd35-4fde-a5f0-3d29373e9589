package com.xinfei.vocprod.test.service;

import com.xinfei.vocprod.biz.service.impl.TeamServiceImpl;
import com.xinfei.vocprod.facade.rr.dto.TeamDto;
import com.xinfei.vocprod.test.base.AdvanceDevTestBase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

public class TeamServiceImplTest extends AdvanceDevTestBase {

    @InjectMocks
    private TeamServiceImpl teamService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试查询团队信息
     */
    @Test
    public void testQuery() {
        // 准备测试数据
        String teamCode = "test";

        // 执行测试
        TeamDto result = teamService.query(teamCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(teamCode, result.getTeamCode());
        assertEquals("信飞技术中心", result.getTeamName());
    }

    /**
     * 测试查询团队信息 - 空团队代码
     */
    @Test
    public void testQuery_EmptyTeamCode() {
        // 准备测试数据
        String teamCode = "";

        // 执行测试
        TeamDto result = teamService.query(teamCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(teamCode, result.getTeamCode());
        assertEquals("信飞技术中心", result.getTeamName());
    }

    /**
     * 测试查询团队信息 - null团队代码
     */
    @Test
    public void testQuery_NullTeamCode() {
        // 准备测试数据
        String teamCode = null;

        // 执行测试
        TeamDto result = teamService.query(teamCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(teamCode, result.getTeamCode());
        assertEquals("信飞技术中心", result.getTeamName());
    }
}
