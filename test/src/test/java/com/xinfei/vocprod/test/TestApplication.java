/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.test;


import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;

@SpringBootApplication(scanBasePackages = {"com.xinfei.vocprod.biz",
        "com.xinfei.vocprod.test.base"})
@MapperScan("com.xinfei.vocprod.dal.mapper")
@ServletComponentScan(basePackages = "com.xinfei.vocprod.test.base")
public class TestApplication {

    /**
     * 日志句柄
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(TestApplication.class);

    /**
     * 系统启动入口
     *
     * @param args 系统启动参数
     */
    public static void main(String[] args) {
        LOGGER.info("系统启动开始");
        try {
            // 启动spring boot
            SpringApplication.run(TestApplication.class, args);

            // TODO 预留业务初始化 系统变量、全局配置等等

        } catch (Throwable t) {
            LOGGER.error( "系统启动异常",t);
            System.exit(1);
        }
        LOGGER.info("系统启动成功!!!");
    }

}
