package com.xinfei.vocprod.test.service;

import com.xinfei.vocprod.biz.common.RandomBizKey;
import com.xinfei.vocprod.biz.service.impl.CommonServiceImpl;
import com.xinfei.vocprod.facade.rr.GetABRequest;
import com.xinfei.vocprod.itl.impl.RandomGeneratorClientImpl;
import com.xinfei.vocprod.test.base.AdvanceDevTestBase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * CommonServiceImpl单元测试
 *
 * <AUTHOR>
 * @version $ CommonServiceImplTest, v 0.1 2024-12-20 14:03 AI Exp $
 */
public class CommonServiceImplTest extends AdvanceDevTestBase {

    @InjectMocks
    private CommonServiceImpl commonService;

    @Mock
    private RandomGeneratorClientImpl randomGeneratorClient;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常场景 - 获取AB分组
     */
    @Test
    public void testGetGroupAB_Normal() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("123456789");
        request.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 模拟依赖行为
        when(randomGeneratorClient.ab(eq("123456789"), eq(RandomBizKey.KFDT_HYJZX))).thenReturn("groupA");

        // 执行测试
        String result = commonService.getGroupAB(request);

        // 验证结果
        assertEquals("groupA", result);
        verify(randomGeneratorClient, times(1)).ab(eq("123456789"), eq(RandomBizKey.KFDT_HYJZX));
    }

    /**
     * 测试场景 - 获取AB分组返回groupB
     */
    @Test
    public void testGetGroupAB_ReturnGroupB() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("987654321");
        request.setBizKey(RandomBizKey.KFDT_CNXW_DMX);

        // 模拟依赖行为
        when(randomGeneratorClient.ab(eq("987654321"), eq(RandomBizKey.KFDT_CNXW_DMX))).thenReturn("groupB");

        // 执行测试
        String result = commonService.getGroupAB(request);

        // 验证结果
        assertEquals("groupB", result);
        verify(randomGeneratorClient, times(1)).ab(eq("987654321"), eq(RandomBizKey.KFDT_CNXW_DMX));
    }

    /**
     * 测试异常场景 - RandomGeneratorClient抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testGetGroupAB_Exception() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("123456789");
        request.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 模拟依赖行为抛出异常
        when(randomGeneratorClient.ab(anyString(), anyString())).thenThrow(new RuntimeException("模拟异常"));

        // 执行测试 - 应该抛出异常
        commonService.getGroupAB(request);
    }

    /**
     * 测试边界场景 - 用户编号为空
     */
    @Test
    public void testGetGroupAB_EmptyUserNo() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("");
        request.setBizKey(RandomBizKey.KFDT_HYJZX);

        // 模拟依赖行为
        when(randomGeneratorClient.ab(eq(""), eq(RandomBizKey.KFDT_HYJZX))).thenReturn("defaultGroup");

        // 执行测试
        String result = commonService.getGroupAB(request);

        // 验证结果
        assertEquals("defaultGroup", result);
        verify(randomGeneratorClient, times(1)).ab(eq(""), eq(RandomBizKey.KFDT_HYJZX));
    }

    /**
     * 测试边界场景 - 业务键为空
     */
    @Test
    public void testGetGroupAB_EmptyBizKey() {
        // 准备测试数据
        GetABRequest request = new GetABRequest();
        request.setUserNo("123456789");
        request.setBizKey("");

        // 模拟依赖行为
        when(randomGeneratorClient.ab(eq("123456789"), eq(""))).thenReturn("defaultGroup");

        // 执行测试
        String result = commonService.getGroupAB(request);

        // 验证结果
        assertEquals("defaultGroup", result);
        verify(randomGeneratorClient, times(1)).ab(eq("123456789"), eq(""));
    }

    /**
     * 测试边界场景 - 请求参数为null
     */
    @Test
    public void testGetGroupAB_NullRequest() {
        // 执行测试 - 应该返回null或抛出异常，取决于实现
        try {
            String result = commonService.getGroupAB(null);
            assertNull(result);
        } catch (NullPointerException e) {
            // 如果实现选择抛出异常，这也是可接受的
        }
    }
}
