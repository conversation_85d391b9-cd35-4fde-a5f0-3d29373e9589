@MockBean, @Mock, 和 @InjectMocks 是在不同上下文中使用的注解，通常用于测试和集成测试中。它们分别属于 Spring Boot Test 和 Mockito 框架。


@MockBean:

@MockBean 是 Spring Boot Test 中的注解，用于创建模拟对象并将它们注册到 Spring 的应用上下文中。
通常在集成测试中使用，用于替代真实的 bean，以便在测试中模拟某些行为。
可以用于替代 Spring 上下文中的 bean，例如数据库访问对象（DAO）、服务等。
~~~~~
@SpringBootTest
public class MyServiceIntegrationTest {

    @MockBean
    private MyRepository myRepository; // 模拟 MyRepository bean

    // 测试代码
}
~~~~~

@Mock:

@Mock 是 Mockito 框架中的注解，用于创建模拟对象（Mock对象）。
在普通的单元测试中使用，不依赖于 Spring。
主要用于模拟对象的行为。
~~~~~
public class MyServiceTest {

    @Mock
    private MyRepository myRepository; // 创建 MyRepository 的模拟对象

    // 测试代码
}
~~~~~
@InjectMocks:

@InjectMocks 是 Mockito 框架中的注解，用于注入模拟对象到被测试对象中。
与 @Mock 一起使用，将模拟对象注入到被测试对象的字段中。
主要用于自动注入模拟对象，简化测试代码。
~~~~~
public class MyServiceTest {

    @Mock
    private MyRepository myRepository; // 创建 MyRepository 的模拟对象

    @InjectMocks
    private MyService myService; // 将模拟对象注入到 MyService 中

    // 测试代码
}
~~~~~

综合使用这些注解，你可以在 Spring Boot 测试中使用 @MockBean 来替代真实的 bean，而在 Mockito 单元测试中使用 @Mock 和 @InjectMocks 来模拟对象和注入依赖。这有助于确保测试的独立性和可控性。
