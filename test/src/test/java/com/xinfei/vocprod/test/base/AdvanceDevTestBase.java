/**
 * Xinfei.com Inc. Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.test.base;

import com.xinfei.vocprod.test.TestApplication;
import com.xinfei.xfframework.test.DevTestBase;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * 测试基类
 *
 * <p>在原有的单测基类上进行封装，为了让研发更快的编写测试用例</p>
 *
 * <AUTHOR>
 * @version $ AdvanceDevTestBase, v 0.1 2023/12/18 21:42 平仙 Exp $
 */
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@ExtendWith(SpringExtension.class)
@ActiveProfiles({"test"})
@RunWith(MockitoJUnitRunner.class)
@TestExecutionListeners(MockitoTestExecutionListener.class)
public abstract class AdvanceDevTestBase extends DevTestBase {

}
