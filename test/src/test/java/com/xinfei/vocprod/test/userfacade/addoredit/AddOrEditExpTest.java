/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.test.userfacade.addoredit;

import com.xinfei.vocprod.facade.UserFacade;
import com.xinfei.vocprod.test.base.AdvanceDevTestBase;
import com.xinfei.xfframework.test.model.PrepareData;
import com.xinfei.xfframework.test.provider.TestDataProvider;
import com.xinfei.xfframework.test.provider.TestDataProviderExt;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.test.context.junit4.SpringRunner;
import org.testng.annotations.Test;

import javax.annotation.Resource;

/**
 * 注意
 * <AUTHOR>
 * @version $ AddOrEditExpTest, v 0.1 2023/12/26 14:30 Chengsheng.Li Exp $
 */
@RunWith(SpringRunner.class)
public class AddOrEditExpTest extends AdvanceDevTestBase {

    /** 用户管理类门面 */
    @Resource
    private UserFacade userFacade;

    @Test(dataProvider = "TestDataProvider", dataProviderClass = TestDataProvider.class)
    @TestDataProviderExt(path = "/testdata/userfacade/addoredit/")
    public void addOrEdit(String caseId, PrepareData prepareData) {
        // 执行测试用例
        runTest(caseId, prepareData, userFacade);
    }
    /**
     * mock 准备
     */
    @Override
    protected void mockPrepare() {
//        BaseResponse<ThreeElementsDTO> response1 = new BaseResponse<>();
//        response1.setSuccess(true);
//        ThreeElementsDTO threeElementsDTO = new ThreeElementsDTO();
//        response1.setData(threeElementsDTO);
//        threeElementsDTO.setIdNo("aa");
//        threeElementsDTO.setMobile("11111111");
//        threeElementsDTO.setName("站三");
//        threeElementsDTO.setCustNo("112233");
//        Mockito.when(standardUserFacade.queryThreeElementsByUserNo(Mockito.any())).thenReturn(response1);
    }
}
