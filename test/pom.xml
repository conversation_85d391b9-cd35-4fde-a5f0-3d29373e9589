<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xinfei.vocprod</groupId>
        <artifactId>vocprod</artifactId>
        <version>1.0.4.20250523-SNAPSHOT</version>
    </parent>

    <artifactId>techplacycore-test</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.xinfei.xfframework</groupId>
            <artifactId>xfframework-context-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- sub dependencies -->
        <dependency>
            <groupId>com.xinfei.vocprod</groupId>
            <artifactId>vocprod-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!-- 执行集成测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.12.4</version>
                <configuration>
                    <skipTests>${isSkipIntegrationTest}</skipTests>
                    <excludes>
                        <exclude>**/*.java</exclude>
                    </excludes>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
                <executions>
                    <execution>
                        <id>test-testng</id>
                        <phase>test</phase>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <configuration>
                            <junitArtifactName>none:none</junitArtifactName>
                            <testNGArtifactName>org.testng:testng</testNGArtifactName>
                            <!-- 只有在argLine里指定的参数，在使用surefire测试的时候才能生效 -->
                            <argLine>
                                -Xmx1024m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m -Dfile.encoding=UTF-8
                            </argLine>
                            <suiteXmlFiles>
                                <!-- 这里需要根据自己配置的testng的xml指定对应的路径 -->
                                <file>src/test/resources/testng-all.xml</file>
                            </suiteXmlFiles>
                            <testFailureIgnore>true</testFailureIgnore>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
