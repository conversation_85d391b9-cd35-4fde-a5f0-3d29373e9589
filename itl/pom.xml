<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xinfei.vocprod</groupId>
        <artifactId>vocprod</artifactId>
        <version>1.0.4.20250523-SNAPSHOT</version>
    </parent>
    <artifactId>vocprod-itl</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.xinfei.vocprod</groupId>
            <artifactId>vocprod-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xinfei.vocprod</groupId>
            <artifactId>vocprod-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xinfei.xfframework</groupId>
            <artifactId>xfframework-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xinfei.psenginecore</groupId>
            <artifactId>psenginecore-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xinfei.cashiercore</groupId>
            <artifactId>cashiercore-common-service-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xinfei.vipcore</groupId>
            <artifactId>vipcore-facade</artifactId>
        </dependency>
        <!--cis查询相关-->
        <dependency>
            <groupId>com.xyf.user</groupId>
            <artifactId>cis-query-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xyf.user</groupId>
            <artifactId>user-auth-core-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xyf.user</groupId>
            <artifactId>cis-ext-info-facade</artifactId>
        </dependency>

        <dependency>
            <artifactId>cis-secure-client</artifactId>
            <groupId>com.xyf.user</groupId>
        </dependency>

        <dependency>
            <groupId>com.xinfei.cisaggs</groupId>
            <artifactId>cisaggs-facade</artifactId>
        </dependency>
        <!--lcs-->
        <dependency>
            <groupId>io.kyoto.pillar</groupId>
            <artifactId>lcs-api</artifactId>
            <version>1.0.27-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>io.kyoto</groupId>
                    <artifactId>sole-api</artifactId>
                </exclusion>
                <!-- 项目使用log4j2日志，去除springboot默认日志依赖 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.kyoto</groupId>
            <artifactId>sole-api</artifactId>
            <version>6.0.1-apollo-encrypt</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>feign-core</artifactId>
                    <groupId>io.github.openfeign</groupId>
                </exclusion>


                <exclusion>
                    <artifactId>spring-boot-starter-web</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>swagger-annotations</artifactId>
                    <groupId>io.swagger</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 随机数系统 -->
        <dependency>
            <groupId>com.xyf.common</groupId>
            <artifactId>xf-random-generator-client</artifactId>
            <version>20250610.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.xinfei.supervip</groupId>
            <artifactId>supervip-interfaces</artifactId>
            <version>${supervip-interfaces.version}</version>
        </dependency>
    </dependencies>
</project>
