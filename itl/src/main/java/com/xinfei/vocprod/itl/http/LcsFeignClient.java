package com.xinfei.vocprod.itl.http;

import com.xinfei.vocprod.itl.Constants;
import com.xinfei.vocprod.itl.rr.PlanDetailResponse;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@FeignClient(name = Constants.LCS, contextId = Constants.LCS, path = "/")
public interface LcsFeignClient {
    /**
     * LCS批量查询借据、账单信息
     * <a href="https://www.tapd.cn/20090981/markdown_wikis/show/#1120090981001014451@toc82">接口文档</a>
     * <a href="https://dev-lcs.devxinfei.cn/doc.html#/%E5%88%86%E7%BB%84%E5%90%8D%E7%A7%B0/%E5%80%9F%E6%8D%AE%E6%9F%A5%E8%AF%A2%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/batchLoanQueryUsingPOST">swagger文档</a>
     * <a href="https://confluence.joyborrow.com/pages/viewpage.action?pageId=39786846">接口与客服字段对应</a>
     *
     * @param request
     * @return
     */
    @PostMapping("/lcs/loan/plan-detail")
    PlanDetailResponse planDetail(LoanPlanRequest request);
}
