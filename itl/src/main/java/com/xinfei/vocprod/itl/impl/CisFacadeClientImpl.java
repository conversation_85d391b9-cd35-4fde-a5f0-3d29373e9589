/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.impl;

import com.xinfei.cisaggs.facade.CisFacade;
import com.xinfei.cisaggs.facade.rr.StandardBatchCustomerInfoRequest;
import com.xinfei.cisaggs.facade.rr.StandardBatchCustomerInfoResponse;
import com.xinfei.cisaggs.facade.rr.dto.CustomerQueryDTO;
import com.xinfei.cisaggs.facade.rr.dto.StandardCustInfoQueryItem;
import com.xinfei.vocprod.itl.CisCommonAttributes;
import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.util.LogUtil;
import com.xyf.cis.SecureClient;
import com.xyf.cis.dto.SecureEncryptDTO;
import com.xyf.cis.query.facade.SearchFacade;
import com.xyf.cis.query.facade.UserQueryFacade;
import com.xyf.cis.query.facade.dto.request.QueryUserNoRequest;
import com.xyf.cis.query.facade.dto.standard.request.UserSearchRequest;
import com.xyf.cis.query.facade.dto.standard.response.LastLoginDTO;
import com.xyf.cis.query.facade.dto.standard.response.RegisterInfoDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.ext.info.dto.request.QueryChallengeRequest;
import com.xyf.ext.info.facade.ExtInfoFacade;
import com.xyf.ext.info.model.Challenge;
import com.xyf.user.auth.dto.nonStandard.response.AuthIdcardDTO;
import com.xyf.user.auth.dto.request.QueryLatestPassAuthIdCardRequest;
import com.xyf.user.auth.facade.UserAuthFacade;
import com.xyf.user.facade.common.model.BaseResponse;
import com.xyf.user.facade.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ CisFacadeClientImpl, v 0.1 2023-12-20 14:10 junjie.yan Exp $
 */

@Component
@Slf4j
public class CisFacadeClientImpl {

    @Resource
    private UserQueryFacade userQueryFacade;

    @Resource
    private UserAuthFacade userAuthFacade;

    @Resource
    private SearchFacade searchFacade;

    @Resource
    private ExtInfoFacade extInfoFacade;

    @Resource
    private SecureClient secureClient;

    @Resource
    private CisFacade cisFacade;


    public RegisterInfoDTO queryRegisterInfoByUserNo(Long userNo) {
        BaseResponse<RegisterInfoDTO> response = null;
        String msg = "UserQueryFacade.queryRegisterInfoByUserNoV2:";
        QueryUserNoRequest request = new QueryUserNoRequest();
        request.setUserNo(userNo);
        CisCommonAttributes.setCommonAttributes(request);
        try {
            response = userQueryFacade.queryRegisterInfoByUserNoV2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryRegisterInfoByUserNoV2", request, response));
            if (response == null || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryRegisterInfoByUserNoV2", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public AuthIdcardDTO queryLatestPassAuthIdCard(QueryLatestPassAuthIdCardRequest request) {
        BaseResponse<List<AuthIdcardDTO>> response = null;
        String msg = "UserAuthFacade.queryLatestPassAuthIdCard:";
        CisCommonAttributes.setCommonAttributes(request);
        try {
            response = userAuthFacade.queryLatestPassAuthIdCard(request);
            log.info(LogUtil.clientLog("UserAuthFacade", "queryLatestPassAuthIdCard", request, response));
            if (!response.isSuccess()) {
                log.warn("queryLatestPassAuthIdCard,查询用户最近一次认证的身份证失败,req:{},resp:{}", request, response);
                return null;
            }

            log.info("queryLatestPassAuthIdCard,查询用户最近一次认证的身份证成功,req:{},resp:{}", request, response);
            if (ObjectUtils.isEmpty(response.getData())) {
                return null;
            }
            return response.getData().get(0);
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserAuthFacade", "queryLatestPassAuthIdCard", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public PageResult<UserSearchDTO> queryUserList(String mobileNo, String custNo, String userNo, Integer pageNum, Integer pageSize) {
        BaseResponse<PageResult<UserSearchDTO>> response = null;
        UserSearchRequest request = new UserSearchRequest();
        if (StringUtils.isNotBlank(mobileNo)) {
            request.setMobileNo(mobileNo);
        }
        request.setCustNo(custNo);
        if (StringUtils.isNotBlank(userNo)) {
            request.setUserNo(Long.parseLong(userNo));
        }
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        CisCommonAttributes.setCommonAttributes(request);
        String msg = "SearchFacade.userList:";
        try {
            response = searchFacade.userList(request);
            log.info(LogUtil.clientLog("SearchFacade", "userList", request, response));
            if (response == null || response.isNotSuccess() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("SearchFacade", "userList", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public LastLoginDTO queryLastLoginByUserNo(Long userNo) {
        if (Objects.isNull(userNo)) {
            return null;
        }
        QueryUserNoRequest request = new QueryUserNoRequest();
        request.setUserNo(userNo);
        CisCommonAttributes.setCommonAttributes(request);
        BaseResponse<LastLoginDTO> response = null;
        String msg = "UserQueryFacade.queryLastLoginByUserNoV2:";
        try {
            response = userQueryFacade.queryLastLoginByUserNoV2(request);
            log.info(LogUtil.clientLog("UserQueryFacade", "queryLastLoginByUserNoV2", request, response));
            if (Objects.isNull(response) || Objects.isNull(response.getData()) || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("UserQueryFacade", "queryLastLoginByUserNoV2", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Challenge queryLatestUserExtInfoData(String custNo, String app) {
        BaseResponse<Challenge> response = null;
        QueryChallengeRequest request = new QueryChallengeRequest();
        request.setApp(app);
        request.setCustNo(custNo);
        CisCommonAttributes.setCommonAttributes(request);
        String msg = "ExtInfoFacade.queryLatestUserExtInfoData:";
        try {
            response = extInfoFacade.queryLatestUserExtInfoData(request);
            log.info(LogUtil.clientLog("ExtInfoFacade", "queryLatestUserExtInfoData", request, response));
            if (response == null || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("ExtInfoFacade", "queryLatestUserExtInfoData", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public StandardBatchCustomerInfoResponse queryStandardBatchCustomerInfo(String custNo, String app) {
        BaseResponse<StandardBatchCustomerInfoResponse> response = null;

        CustomerQueryDTO customerQueryDTO = new CustomerQueryDTO();
        customerQueryDTO.setCustNo(custNo);
        if ("xyf".equals(app)) {
            customerQueryDTO.setApp("xyf01");
        } else {
            customerQueryDTO.setApp(app);
        }

        StandardBatchCustomerInfoRequest request = new StandardBatchCustomerInfoRequest();
        request.setRequestList(Collections.singletonList(customerQueryDTO));
        request.setCustQueryItems(Arrays.asList(StandardCustInfoQueryItem.BASE_INFO_RESULT, StandardCustInfoQueryItem.CONTACT_RESULT));

        CisCommonAttributes.setCommonAttributes(request);
        String msg = "CisFacade.queryStandardBatchCustomerInfo:";
        try {
            response = cisFacade.queryStandardBatchCustomerInfo(request);
            log.info(LogUtil.clientLog("CisFacade", "queryStandardBatchCustomerInfo", request, response));
            if (response == null || response.isNotSuccess()) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("CisFacade", "queryStandardBatchCustomerInfo", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public String getEncodeMobileLocal(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return "";
        }
        List<SecureEncryptDTO> secureEncryptDTOS = batchEncryptLocal(Collections.singletonList(mobile));
        if (CollectionUtils.isNotEmpty(secureEncryptDTOS) && secureEncryptDTOS.get(0) != null) {
            return secureEncryptDTOS.get(0).getCipherText();
        }
        return "";
    }

    public List<SecureEncryptDTO> batchEncryptLocal(List<String> mobiles) {
        List<SecureEncryptDTO> response = null;
        String msg = "SecureClient.batchEncryptLocal:";
        try {
            response = secureClient.batchEncrypt(mobiles);
            log.info(LogUtil.clientLog("SecureClient", "batchEncryptLocal", mobiles, response));
            return response;
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("SecureClient", "batchEncryptLocal", mobiles, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }
}