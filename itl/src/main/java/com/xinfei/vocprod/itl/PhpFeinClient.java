/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.itl;

import org.springframework.cloud.openfeign.FeignClient;

/*
 * 注意FeignClient注解需要配置三个参数
 * name 为应用名
 * path 为父路径
 * url 为sevice对应的域名 域名可以配置在配置中心中，格式${服务名.url}
 * 注意contextId表示此feignclient对应的配置id，推荐的格式是contextId=name+"-"+ 类名去掉后缀，此contextid 用来配置此feignclient的超时时间
 *feign.client.config.“SERVICE_NAME”.connectTimeout= *** 默认是10s
 *feign.client.config.“SERVICE_NAME”readTimeout= **** 默认是60s
 * 默认连接池默认存活时间是900秒
 * 理论上对外暴露的所有接口都是post类型的，需要指定使用PostMapping注解
 * <AUTHOR>
 * @version $ Constants, v 0.1 2023/8/29 09:54 Chengsheng.Li Exp $
 */
@FeignClient(name = Constants.SERVICE_NAME,contextId= Constants.SERVICE_NAME+".PhpFeinClient",path ="/php"  )
public interface PhpFeinClient {
}
