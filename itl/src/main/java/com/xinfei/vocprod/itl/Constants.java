/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.itl;

/*
 * 此类主要是保存一些常量信息，如应用名
 *
 * <AUTHOR>
 * @version $ Constants, v 0.1 2023/8/29 09:54 Chengsheng.Li Exp $
 */
public class Constants {
    /*php类型应用名*/
    public static final String SERVICE_NAME="phpdemo";

    /** 工单系统 */
    public static final String WORK_ORDER = "work-order";

    /** 客服系统 */
    public static final String VOCMNG = "vocmng";

    /** 工单系统成功响应码 */
    public static final String WORK_ORDER_SUCCESS_CODE = "000000";

    /** 客服系统成功响应码 */
    public static final Integer VOCMNG_SUCCESS_CODE = 200;

    /** nPay服务 */
    public static final String N_PAY = "npay-api";

    /** 大模型服务 */
    public static final String DMX = "dmx";

    /** 特征平台 */
    public static final String FEATURE_QUERY = "feature-query";

    /** 借据账单系统应用名称 */
    public static final String LCS = "lcs";

    /** 账户系统 */
    public static final String RCS_PROVIDER = "rcs-provider";
}
