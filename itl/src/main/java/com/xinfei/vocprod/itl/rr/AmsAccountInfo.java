/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ Account, v 0.1 2023/10/26 8:13 PM longjie.yuan Exp $
 */
@Data
public class AmsAccountInfo {
    /**
     * 客户号
     */
    @JsonProperty("cust_no")
    private String custNo;

    /**
     * 授信额度，单位是分
     */
    @JsonProperty("credit_amt")
    private BigDecimal creditAmt;

    /**
     * 已贷额度，单位是分
     */
    @JsonProperty("debit_amt")
    private BigDecimal debitAmt;

}