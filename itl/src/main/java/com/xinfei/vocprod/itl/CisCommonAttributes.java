/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.itl;

import com.xinfei.common.lang.context.BizTraceContext;
import com.xinfei.supervip.common.model.SvcBaseRequest;
import com.xinfei.vipcore.facade.rr.VcBaseRequest;
import com.xyf.user.facade.common.model.BaseCisRequest;

import java.util.UUID;

/**
 *
 * <AUTHOR>
 * @version $ CisCommonAttributes, v 0.1 2025-04-02 15:12 junjie.yan Exp $
 */

public class CisCommonAttributes {

    private static final String upstreamService = "vocprod";

    // 产品码
    private static final String cnlPdCode = "APP001000007";

    // 事件码
    private static final String cnlEvCode = "CD00200052";

    // 统一设置公共属性的工具方法
    public static <T extends BaseCisRequest> void setCommonAttributes(T request) {
        BizTraceContext bizTraceContext = new BizTraceContext();
        bizTraceContext.setCnlPdCode(cnlPdCode);
        bizTraceContext.setCnlEvCode(cnlEvCode);
        request.setUpstreamService(upstreamService);
        request.setBizTraceContext(bizTraceContext);
    }

    public static <T extends SvcBaseRequest> void setVipCardCommonAttributes(T request) {
        BizTraceContext bizTraceContext = new BizTraceContext();
        bizTraceContext.setCnlPdCode(cnlPdCode);
        bizTraceContext.setCnlEvCode(cnlEvCode);
        request.setBizChannel(upstreamService);
        request.setRequestId(UUID.randomUUID().toString());
        request.setBizTraceContext(bizTraceContext);
    }

    public static <T extends VcBaseRequest> void setVipCardCommonAttributes(T request) {
        BizTraceContext bizTraceContext = new BizTraceContext();
        bizTraceContext.setCnlPdCode(cnlPdCode);
        bizTraceContext.setCnlEvCode(cnlEvCode);
        request.setRequestId(UUID.randomUUID().toString());
        request.setBizTraceContext(bizTraceContext);
        request.setUa(upstreamService);
    }


}