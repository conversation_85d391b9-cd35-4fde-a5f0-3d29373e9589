/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.impl;

import com.xinfei.vocprod.itl.WorkOrderFeignClient;
import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.itl.rr.*;
import com.xinfei.vocprod.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ WorkOrderFeignClientImpl, v 0.1 2024-01-17 15:10 junjie.yan Exp $
 */
@Slf4j
@Component
public class WorkOrderFeignClientImpl {

    @Resource
    private WorkOrderFeignClient workOrderFeignClient;

    public void createTask(CreateTaskDto request) {
        WorkOrderResponse<Object> response = null;
        String msg = "WorkOrderFeignClient.createTask:";
        try {
            response = workOrderFeignClient.createTask(request);
            log.info(LogUtil.clientLog("WorkOrderFeignClient", "createTask", request, response));
            if (Objects.isNull(response) || !"000000".equals(response.getCode()) || !"请求成功".equals(response.getMessage())) {
                if (response != null && response.getData() instanceof CreateTaskResp) {
                    CreateTaskResp createTaskResp = (CreateTaskResp) response.getData();
                    msg += createTaskResp.getMsg();
                } else {
                    msg += response == null ? "response is null" : response.getMessage();
                }
                throw new Exception(msg);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("WorkOrderFeignClient", "createTask", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public GetTaskByUserIdResp getTaskByUserNo(Long userNo) {
        WorkOrderResponse<GetTaskByUserIdResp> response = null;
        String msg = "WorkOrderFeignClient.getTaskByUserId:";
        GetTaskByUserIdReq request = new GetTaskByUserIdReq();
        request.setUserId(userNo);
        try {
            response = workOrderFeignClient.getTaskByUserId(request);
            log.info(LogUtil.clientLog("WorkOrderFeignClient", "getTaskByUserId", request, response));
            if (Objects.isNull(response) || !"000000".equals(response.getCode()) || !"请求成功".equals(response.getMessage()) || Objects.isNull(response.getData())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("WorkOrderFeignClient", "getTaskByUserId", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

}