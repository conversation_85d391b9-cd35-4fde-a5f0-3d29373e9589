/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.impl;

import cn.xinfei.usergroup.random.client.AbClient;
import cn.xinfei.usergroup.random.client.bo.AbBO;
import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ CisFacadeClientImpl, v 0.1 2023-12-20 14:10 junjie.yan Exp $
 */

@Component
@Slf4j
public class RandomGeneratorClientImpl {

    public String ab(String userNo, String bizKey) {
        AbBO response = null;
        String msg = "AbClient.ab:";
        try {
            response = AbClient.ab(bizKey, userNo);
            log.info(LogUtil.clientLog("AbClient", "ab", bizKey, response));
            return response.getGroup();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("AbClient", "ab", bizKey, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

}