/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.impl;

import com.xinfei.vocprod.facade.rr.ScreenshotConfineRequest;
import com.xinfei.vocprod.itl.VocmngFeignClient;
import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.itl.rr.GetUserNoListRequest;
import com.xinfei.vocprod.itl.rr.IVRLatestBillDto;
import com.xinfei.vocprod.itl.rr.IVRLoanSettlementDto;
import com.xinfei.vocprod.itl.rr.IVRMonthlyAmountDto;
import com.xinfei.vocprod.itl.rr.IVROrderInfoDto;
import com.xinfei.vocprod.itl.rr.IdCardVerifyRequest;
import com.xinfei.vocprod.itl.rr.SummaryLogsReq;
import com.xinfei.vocprod.itl.rr.UdeskSendMessageRequest;
import com.xinfei.vocprod.itl.rr.UserLabelRequest;
import com.xinfei.vocprod.itl.rr.UserNoAppDto;
import com.xinfei.vocprod.itl.rr.UserNosRequest;
import com.xinfei.vocprod.itl.rr.UserStatusRequest;
import com.xinfei.vocprod.itl.rr.VocmngResponse;
import com.xinfei.vocprod.util.LogUtil;
import com.xinfei.vocprod.util.enums.ErrDtlEnum;
import com.xinfei.vocprod.util.exception.VocprodException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ WorkOrderFeignClientImpl, v 0.1 2024-01-17 15:10 junjie.yan Exp $
 */
@Slf4j
@Component
public class VocmngFeignClientImpl {

    @Resource
    private VocmngFeignClient vocmngFeignClient;

    public Integer summaryLogs(Long userNo) {
        VocmngResponse<Integer> response = null;
        SummaryLogsReq request = new SummaryLogsReq();
        request.setUserNo(userNo);
        String msg = "VocmngFeignClient.summaryLogs:";
        try {
            response = vocmngFeignClient.summaryLogs(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "summaryLogs", request, response));
            if (Objects.isNull(response) || response.isFailed() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "summaryLogs", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public List<UserNoAppDto> getUserNoList(String mobile) {
        VocmngResponse<List<UserNoAppDto>> response = null;
        GetUserNoListRequest request = new GetUserNoListRequest();
        request.setMobile(mobile);
        String msg = "VocmngFeignClient.getUserNoList:";
        try {
            response = vocmngFeignClient.getUserNoList(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "getUserNoList", request, response));
            if (Objects.isNull(response) || response.isFailed() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "getUserNoList", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public String getUserLabel(Long userNo) {
        VocmngResponse<String> response = null;
        UserLabelRequest request = new UserLabelRequest();
        request.setUserNo(userNo);
        String msg = "VocmngFeignClient.getUserLabel:";
        try {
            response = vocmngFeignClient.getUserLabel(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "getUserLabel", request, response));
            if (Objects.isNull(response) || response.isFailed() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "getUserLabel", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Boolean getUserStatus(String mobile) {
        VocmngResponse<Boolean> response = null;
        UserStatusRequest request = new UserStatusRequest();
        request.setMobile(mobile);
        String msg = "VocmngFeignClient.getUserStatus:";
        try {
            response = vocmngFeignClient.getUserStatus(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "getUserStatus", request, response));
            if (Objects.isNull(response) || response.isFailed() || response.getData() == null) {
                return Boolean.FALSE;
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "getUserStatus", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public IVROrderInfoDto queryIVROrderList(String customerNo, String mobile) {
        VocmngResponse<IVROrderInfoDto> response = null;
        UserNosRequest request = new UserNosRequest();
        request.setMobile(mobile);
        request.setCustomNo(customerNo);
        String msg = "VocmngFeignClient.queryIVROrderList:";
        try {
            response = vocmngFeignClient.queryIVROrderList(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "queryIVROrderList", request, response));
            if (Objects.isNull(response) || response.isFailed() || response.getData() == null) {
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "queryIVROrderList", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public IVRLatestBillDto queryIVRLatestBill(String customerNo, String mobile) {
        VocmngResponse<IVRLatestBillDto> response = null;
        UserNosRequest request = new UserNosRequest();
        request.setMobile(mobile);
        request.setCustomNo(customerNo);
        String msg = "VocmngFeignClient.queryIVRLatestBill:";
        try {
            response = vocmngFeignClient.queryIVRLatestBill(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "queryIVRLatestBill", request, response));
            if (Objects.isNull(response) || response.isFailed() || response.getData() == null) {
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "queryIVRLatestBill", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public IVRMonthlyAmountDto queryIVRMonthlyAmount(String customerNo, String mobile) {
        VocmngResponse<IVRMonthlyAmountDto> response = null;
        UserNosRequest request = new UserNosRequest();
        request.setMobile(mobile);
        request.setCustomNo(customerNo);
        String msg = "VocmngFeignClient.queryIVRMonthlyAmount:";
        try {
            response = vocmngFeignClient.queryIVRMonthlyAmount(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "queryIVRMonthlyAmount", request, response));
            if (Objects.isNull(response) || response.isFailed() || response.getData() == null) {
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "queryIVRMonthlyAmount", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public IVRLoanSettlementDto queryIVRLoanSettlement(String customerNo, String mobile) {
        VocmngResponse<IVRLoanSettlementDto> response = null;
        UserNosRequest request = new UserNosRequest();
        request.setMobile(mobile);
        request.setCustomNo(customerNo);
        String msg = "VocmngFeignClient.queryIVRLoanSettlement:";
        try {
            response = vocmngFeignClient.queryIVRLoanSettlement(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "queryIVRLoanSettlement", request, response));
            if (Objects.isNull(response) || response.isFailed() || response.getData() == null) {
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "queryIVRLoanSettlement", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public void sendSms(UdeskSendMessageRequest request) {
        VocmngResponse<Void> response = null;
        String msg = "VocmngFeignClient.queryIVRLoanSettlement:";
        try {
            response = vocmngFeignClient.sendSms(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "sendSms", request, response));
            if (Objects.isNull(response) || response.isFailed()) {
                throw new VocprodException(ErrDtlEnum.REMOTE_CLIENT_EXCEPTION);
            }
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "queryIVRLoanSettlement", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Boolean verifyIdCardLast6(String mobile, String idCardLast6) {
        VocmngResponse<Boolean> response = null;
        IdCardVerifyRequest request = new IdCardVerifyRequest();
        request.setIdCardLast6(idCardLast6);
        request.setMobile(mobile);
        String msg = "VocmngFeignClient.verifyIdCardLast6:";
        try {
            response = vocmngFeignClient.verifyIdCardLast6(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "verifyIdCardLast6", request, response));
            if (Objects.isNull(response) || response.isFailed() || response.getData() == null) {
                return Boolean.FALSE;
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "verifyIdCardLast6", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public Boolean getScreenshotConfine(ScreenshotConfineRequest request) {
        VocmngResponse<Boolean> response = null;
        String msg = "VocmngFeignClient.getScreenshotConfine:";
        try {
            response = vocmngFeignClient.getScreenshotConfine(request);
            log.info(LogUtil.clientLog("VocmngFeignClient", "getScreenshotConfine", request, response));
            if (Objects.isNull(response) || response.isFailed() || response.getData() == null) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VocmngFeignClient", "getUserLabel", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }
}