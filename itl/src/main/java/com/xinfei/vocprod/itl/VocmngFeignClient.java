package com.xinfei.vocprod.itl;

import com.xinfei.vocprod.itl.rr.GetUserNoListRequest;
import com.xinfei.vocprod.itl.rr.IVRLatestBillDto;
import com.xinfei.vocprod.itl.rr.IVRLoanSettlementDto;
import com.xinfei.vocprod.itl.rr.IVRMonthlyAmountDto;
import com.xinfei.vocprod.itl.rr.IVROrderInfoDto;
import com.xinfei.vocprod.itl.rr.IdCardVerifyRequest;
import com.xinfei.vocprod.facade.rr.ScreenshotConfineRequest;
import com.xinfei.vocprod.itl.rr.SummaryLogsReq;
import com.xinfei.vocprod.itl.rr.UdeskSendMessageRequest;
import com.xinfei.vocprod.itl.rr.UserLabelRequest;
import com.xinfei.vocprod.itl.rr.UserNoAppDto;
import com.xinfei.vocprod.itl.rr.UserNosRequest;
import com.xinfei.vocprod.itl.rr.UserStatusRequest;
import com.xinfei.vocprod.itl.rr.VocmngResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ WorkOrderFeignClient, v 0.1 2024/1/15 15:28 qu.lu Exp $
 */
@FeignClient(name = Constants.VOCMNG, contextId = Constants.VOCMNG + ".PhpFeinClient", path = "/")
public interface VocmngFeignClient {
    @PostMapping("/vocmng/summaryLogs")
    VocmngResponse<Integer> summaryLogs(SummaryLogsReq request);

    @PostMapping("/vocmng/getUserLabel")
    VocmngResponse<String> getUserLabel(UserLabelRequest request);

    /**
     * 手机号或身份证号必填其一
     */
    @PostMapping("/vocmng/getUserNoList")
    VocmngResponse<List<UserNoAppDto>> getUserNoList(@RequestBody GetUserNoListRequest request);


    @PostMapping("/vocmng/getUserStatus")
    VocmngResponse<Boolean> getUserStatus(UserStatusRequest req);

    @PostMapping("/vocmng/queryIVROrderList")
    VocmngResponse<IVROrderInfoDto> queryIVROrderList(UserNosRequest req);

    @PostMapping("/vocmng/queryIVRLatestBill")
    VocmngResponse<IVRLatestBillDto> queryIVRLatestBill(UserNosRequest req);

    @PostMapping("/vocmng/queryIVRMonthlyAmount")
    VocmngResponse<IVRMonthlyAmountDto> queryIVRMonthlyAmount(UserNosRequest req);

    @PostMapping("/vocmng/queryIVRLoanSettlement")
    VocmngResponse<IVRLoanSettlementDto> queryIVRLoanSettlement(UserNosRequest req);

    @PostMapping("/vocmng/verifyIdCardLast6")
    VocmngResponse<Boolean> verifyIdCardLast6(IdCardVerifyRequest req);

    @PostMapping("/vocmng/getScreenshotConfine")
    VocmngResponse<Boolean> getScreenshotConfine(ScreenshotConfineRequest request);

    @PostMapping("/vocmng/sendSms")
    VocmngResponse<Void> sendSms(UdeskSendMessageRequest req);
}
