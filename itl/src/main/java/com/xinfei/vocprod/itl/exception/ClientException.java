package com.xinfei.vocprod.itl.exception;

import lombok.Getter;

/**
 * @version 1.0
 * @author：valiant.shaw
 * @date：2023/7/6 14:28
 */
@Getter
public class ClientException extends RuntimeException {


    private final FeignErrDtlEnum errDtlEnum;
    //错误级别
    private final ErrorLevelsEnum errorLevel;

    public ClientException(FeignErrDtlEnum codeEnums, Throwable throwable, ErrorLevelsEnum errorLevel) {
        super(codeEnums.getDescription(), throwable);
        this.errDtlEnum = codeEnums;
        this.errorLevel = errorLevel;
    }

    public ClientException(FeignErrDtlEnum codeEnums, ErrorLevelsEnum errorLevel) {
        super(codeEnums.getDescription());
        this.errDtlEnum = codeEnums;
        this.errorLevel = errorLevel;
    }

    public ClientException(FeignErrDtlEnum codeEnums, String msg, ErrorLevelsEnum errorLevel) {
        super(codeEnums.getDescription() + msg);
        this.errDtlEnum = codeEnums;
        this.errorLevel = errorLevel;
    }


}
