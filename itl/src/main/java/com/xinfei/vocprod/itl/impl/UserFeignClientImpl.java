/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.impl;

import com.xinfei.vocprod.facade.UserFacade;
import com.xinfei.vocprod.facade.rr.ScreenshotConfineRequest;
import com.xinfei.vocprod.facade.rr.ScreenshotConfineResponse;
import com.xinfei.vocprod.facade.rr.UserRequest;
import com.xinfei.vocprod.facade.rr.UserResponse;
import com.xinfei.vocprod.itl.UserFeignClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 用户接口调用实现类
 *
 * <AUTHOR>
 * @version $ TeamqueryByCodeResponse, v 0.1 2023/8/29 09:54 Chengsheng.Li Exp $
 */
@Component
public class UserFeignClientImpl implements UserFeignClient {

    /*user facade对象*/
    @Resource
    private UserFacade userFacade;

    /*
    * 编辑或者更新user 对象
    * @parm request user对象
    * @return user 信息response
    * */
    @Override
    public UserResponse addOrEdit(UserRequest request) {
        return userFacade.addOrEdit(request);
    }


    /*
     * 截屏限制查询
     * @parm request user对象
     * @return user 信息response
     * */
    @Override
    public ScreenshotConfineResponse getScreenshotConfine(ScreenshotConfineRequest request) {
        return userFacade.getScreenshotConfine(request);
    }
}
