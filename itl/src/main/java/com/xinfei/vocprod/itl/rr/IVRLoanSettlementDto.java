/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * IVR在贷未结清订单信息DTO
 *
 * <AUTHOR>
 * @version $ IVRLoanSettlementDto, v 0.1 2025-06-18 shaohui.chen Exp $
 */
@Data
public class IVRLoanSettlementDto {

    @ApiModelProperty(value = "在贷未结清订单数量")
    private Integer unsettledLoanCount;

    @ApiModelProperty(value = "试算结清所需总金额")
    private BigDecimal totalSettlementAmount;

    @ApiModelProperty(value = "是否存在减免方案")
    private Boolean hasReductionPlan;

    @ApiModelProperty(value = "减免方案笔数")
    private Integer planCount;

    @ApiModelProperty(value = "有减免方案的借据总应还金额")
    private BigDecimal planTotalAmount;

    @ApiModelProperty(value = "最早一笔方案到期时间")
    private LocalDate earliestPlanDueDate;
}
