/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.impl;

import com.xinfei.vocprod.itl.DmxFeignClient;
import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.itl.rr.*;
import com.xinfei.vocprod.itl.rr.Process;
import com.xinfei.vocprod.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ DmxFeignClientImpl, v 0.1 2025-04-14 16:40 junjie.yan Exp $
 */
@Component
@Slf4j
public class DmxFeignClientImpl {

    @Resource
    private DmxFeignClient dmxFeignClient;

    public List<ProblemEvaluation> queryGuess(MlMetadata metadata) {
        GueryGuessResp response = null;
        String msg = "DmxFeignClient.queryGuess:";
        GueryGuessReq req = new GueryGuessReq();
        req.setTemplateId("ct_question");
        req.setMetadata(metadata);
        try {
            response = dmxFeignClient.queryGuess(req, "vocmng");
            log.info(LogUtil.clientLog("DmxFeignClient", "queryGuess", req, response));
            if (response == null || !"200".equals(response.getCode())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }

            // 2. 获取响应数据对象
            MlResponse baseResponse = response.getResponse();
            if (baseResponse == null) {
                throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, "Response data is null", ErrorLevelsEnum.ERROR);
            }

            // 3. 获取choices列表
            List<Choice> choices = baseResponse.getChoices();
            if (choices == null || choices.isEmpty()) {
                throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, "Choices list is empty or null", ErrorLevelsEnum.ERROR);
            }

            // 4. 获取第一个choice
            Choice firstChoice = choices.get(0);
            if (firstChoice == null) {
                throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, "First choice is null", ErrorLevelsEnum.ERROR);
            }

            // 5. 获取message对象
            Message message = firstChoice.getMessage();
            if (message == null) {
                throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, "Message is null", ErrorLevelsEnum.ERROR);
            }

            // 6. 获取process对象
            Process process = message.getProcess();
            if (process == null) {
                throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, "Process is null", ErrorLevelsEnum.ERROR);
            }

            return process.getProblem_evaluation();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("DmxFeignClient", "queryGuess", req, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

}