package com.xinfei.vocprod.itl.impl;

import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.itl.http.FeatureQueryClient;
import com.xinfei.vocprod.itl.rr.FeatureQueryReq;
import com.xinfei.vocprod.itl.rr.FeatureQueryResp;
import com.xinfei.vocprod.itl.rr.InputParamsDto;
import com.xinfei.vocprod.itl.rr.ObjType;
import com.xinfei.vocprod.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;


@Slf4j
@Component
public class FeatureQueryClientImpl {
    @Resource
    private FeatureQueryClient featureQueryClient;

    @Value("${appAccessKey}")
    private String appAccessKey;

    @Resource
    private CisFacadeClientImpl cisFacadeClientService;

    public String getCode(String tel, String varCode) {
        String encodeTel = cisFacadeClientService.getEncodeMobileLocal(tel);
        FeatureQueryReq req = new FeatureQueryReq();
        InputParamsDto inputParamsDto = new InputParamsDto();
        inputParamsDto.setFeatureParams(Collections.singletonMap("mobile", new ObjType(encodeTel, "StrValue")));
        inputParamsDto.setFeatureCode(Collections.singletonList(varCode));

        req.setInputParams(inputParamsDto);

        Map<String, ObjType> map = featureQuery(req);
        if (map != null && map.get(varCode) != null) {
            return map.get(varCode).getObj();
        }

        return "";
    }

    public Map<String, ObjType> featureQuery(FeatureQueryReq request) {
        FeatureQueryResp response = null;

        Map<String, Object> headers = new HashMap<>();
        headers.put("appId", "vocprod");
        headers.put("appAccessKey", appAccessKey);
        headers.put("requestId", UUID.randomUUID().toString());
        String msg = "FeatureQueryClient.featureQuery:";
        try {
            response = featureQueryClient.featureQuery(headers, request);
            log.info(LogUtil.clientLog("FeatureQueryClient", "featureQuery", request, response, headers));
            if (Objects.isNull(response) || !response.isSuc() || response.isHasFailed()) {
                msg += response == null ? "response is null" : response.getErrorContext().getErrDesc();
                throw new Exception(msg);
            }
            return response.getFeatureValues();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("FeatureQueryClient", "featureQuery", request, response, msg, headers), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

}
