package com.xinfei.vocprod.itl.http;

import com.xinfei.vocprod.itl.Constants;
import com.xinfei.vocprod.itl.rr.FeatureQueryReq;
import com.xinfei.vocprod.itl.rr.FeatureQueryResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * @since 2025/02/14
 */
@FeignClient(name = Constants.FEATURE_QUERY, contextId = Constants.FEATURE_QUERY, path = "/")
public interface FeatureQueryClient {

    @PostMapping("/api/feature_query")
    FeatureQueryResp featureQuery(@RequestHeader Map<String, Object> headers, FeatureQueryReq request);
}
