package com.xinfei.vocprod.itl.rr;

import com.xinfei.vocprod.itl.Constants;
import lombok.Data;

/**
 * 工单公共响应参数
 *
 * <AUTHOR>
 * @version $ WorkOrderResponse, v 0.1 2024/1/15 15:49 qu.lu Exp $
 */
@Data
public class WorkOrderResponse<T> {
    private String code;
    private String message;
    private T data;

    public boolean isSuccess() {
        return Constants.WORK_ORDER_SUCCESS_CODE.equalsIgnoreCase(code);
    }

    public boolean isFailed() {
        return !isSuccess();
    }
}
