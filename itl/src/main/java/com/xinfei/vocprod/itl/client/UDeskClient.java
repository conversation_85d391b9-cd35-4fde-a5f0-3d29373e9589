/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.xinfei.vocprod.facade.rr.UDeskRequest;
import com.xinfei.vocprod.facade.rr.dto.Customer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;

/**
 * <AUTHOR>
 * @version $ UDeskClient, v 0.1 2024/7/8 14:47 wancheng.qu Exp $
 */
@Component
@Slf4j
public class UDeskClient {
   /* public static void main(String[] args) {
        UDeskRequest u =new UDeskRequest();
        u.setUrlBase("https://1779334.s2.udesk.cn/im_client/?web_plugin_id=55901");
        u.setUpbKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCLG2oxQXc8gSEzPNk04+bcEdujDs0AK2xq0cQSUNDl76EraUdG/t+Sy6vh2R+SjqlD2cHd92jv33zzlg6TVwHykEJ6YRTX9iH2T64xU8zZwfMBG+iIAxtl56xi5XYtlr68eNkFNmZqijcRj+B9drQX2m1OgO2gWvZUk4a8eNklGQIDAQAB");
        u.setImUserKey("a6549fe8579fcbd01b9ca7e02c9ca31e");
        Customer c = new Customer();
        c.setWeb_token("111111115822020");
        c.setC_cf_uid("111111115822020");
        c.setC_cf_dialogueDesc("111111115822020");
        u.setCustomer(c);

        String rsaUrl = getRsaUrl(u);
        System.out.println("res====="+rsaUrl);
    }*/

    public  String getRsaUrl(UDeskRequest customer,boolean enccfUid) {
        String params = String.format(
                "nonce=%s&timestamp=%s&web_token=%s",
                RandomStringUtils.random(5, true, true),
                System.currentTimeMillis(),
                customer.getCustomer().getWeb_token()
        );

        String url = null;
        try {
            url = getUrlSign(customer.getUrlBase(),customer.getImUserKey())
                    + "&customer_encrypt=" + encode(customer.getCustomer(),customer.getUpbKey())
                    + "&" + params
                    + "&encryption_algorithm=SHA256"
                    + "&signature=" + getSignature(params, customer.getImUserKey());
            if(enccfUid){
                url=url+"&c_cf_dialogueDesc="+customer.getCustomer().getC_cf_dialogueDesc();
            }
        } catch (Exception e) {
            log.warn("getRsaUrl encode error,token:{}",customer.getCustomer().getWeb_token(),e);
            throw new RuntimeException("getRsaUrl encode error");
        }
        return url;
    }

    public  String getUrlSign(String baseURL,String im_user_key){
        long timestamp = System.currentTimeMillis();
        // 随机字符串
        String nonce = Long.toString(Double.valueOf(Math.ceil(Math.random() * Long.MAX_VALUE)).longValue(), 36);
        // 合并为需要签名的字符串
        String s = String.format("v_nonce=%s&v_timestamp=%s&%s", nonce, timestamp, im_user_key);
        // 计算sha1，并将结果大写
        String signature = DigestUtils.sha1Hex(s).toUpperCase();
        // 拼接最终的网页插件链接
        return String.format(
                "%s&v_nonce=%s&v_timestamp=%s&v_signature=%s",
                baseURL,
                nonce,
                timestamp,
                signature
        );
    }

    public  String encode(Customer customer,String upbKey) throws Exception {
        // 将 customer 转成 json 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.enable(SerializationFeature.WRAP_ROOT_VALUE);
        String message = objectMapper.writer().writeValueAsString(customer);

        // 获取 RSAPublicKey 对象
        byte[] encoded = Base64.getDecoder().decode(upbKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(encoded);
        RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(keySpec);

        // 初始化 Cipher
        Cipher encryptCipher = Cipher.getInstance("RSA");
        encryptCipher.init(Cipher.ENCRYPT_MODE, publicKey);

        // 对密文分段
        int dataCount = (int) Math.ceil((double) message.getBytes().length / 117);
        byte[][] datas = new byte[dataCount][];
        for (int i = 0; i < dataCount; i++) {
            int length = (i == dataCount - 1) ? (message.getBytes().length % 117) : 117;
            byte[] item = Arrays.copyOfRange(message.getBytes(), i * 117, i * 117 + length);
            datas[i] = item;
        }

        // 对分好段的密文加密，然后合成一个大的 byte 数组，最后将这个大数组使用 base64 编码
        byte[] mi = new byte[]{};
        for (byte[] s : datas) {
            mi = ArrayUtils.addAll(mi, encryptCipher.doFinal(s));
        }
        return Base64.getEncoder().encodeToString(mi);
    }

    public  String getSignature(String paramString,String imUserKey) {
        return DigestUtils.sha256Hex(paramString + "&" + imUserKey).toUpperCase();
    }
}