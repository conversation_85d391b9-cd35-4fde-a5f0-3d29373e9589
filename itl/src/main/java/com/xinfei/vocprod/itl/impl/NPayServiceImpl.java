package com.xinfei.vocprod.itl.impl;

import com.xinfei.vocprod.itl.NPayFeignClient;
import com.xinfei.vocprod.itl.rr.NPayResponse;
import com.xinfei.vocprod.itl.rr.PublicAccountInfo;
import com.xinfei.vocprod.itl.rr.PublicAccountRequest;
import com.xinfei.vocprod.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;


/**
 * <AUTHOR>
 * @version $ NPayServiceImpl, v 0.1 2025/03/20 15:28 pengming.liu Exp $
 */
@Slf4j
@Service
public class NPayServiceImpl {

    @Resource
    private NPayFeignClient nPayFeignClient;

    public PublicAccountInfo queryPublicAccountInfo(PublicAccountRequest request) {
        try {
            request.setRequestId(UUID.randomUUID().toString());
            request.setBizChannel("vocProd");
            NPayResponse<PublicAccountInfo> nPayResponse = nPayFeignClient.queryPublicAccountInfo(request);
            log.info(LogUtil.clientLog("NPayFeignClient", "queryPublicAccountInfo", request, nPayResponse));
            if (nPayResponse == null || !nPayResponse.isSuccess() || nPayResponse.getData() == null) {
                log.error("query queryPublicAccountInfo failed, request={},response={}", request, nPayResponse);
                return null;
            }
            return nPayResponse.getData();
        } catch (Exception e) {
            log.error("queryPublicAccountInfo, request=" + request, e);
            return null;
        }
    }
}
