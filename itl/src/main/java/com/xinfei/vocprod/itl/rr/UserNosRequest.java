/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.rr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户账号请求
 *
 * <AUTHOR>
 * @version $ UserStatusRequest, v 0.1 2025/5/12 16:30 shaohui.chen Exp $
 */
@Data
@ApiModel(description = "用户账号请求")
public class UserNosRequest {

    @ApiModelProperty(value = "用户customer账号")
    private String customNo;

    @ApiModelProperty(value = "用户手机号")
    private String mobile;
}
