package com.xinfei.vocprod.itl.rr;

import com.xinfei.vocprod.itl.Constants;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ NPayResponse, v 0.1 2025/03/21 15:28 pengming.liu Exp $
 */
@Data
public class NPayResponse<T> {
    private String code;
    private String message;
    private T data;

    public boolean isSuccess(){
        return Constants.WORK_ORDER_SUCCESS_CODE.equalsIgnoreCase(code);
    }

    public boolean isFailed(){
        return !isSuccess();
    }
}
