/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.impl;

import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.itl.http.LcsFeignClient;
import com.xinfei.vocprod.itl.rr.PlanDetailResponse;
import com.xinfei.vocprod.util.LogUtil;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LcsFeignService, v 0.1 2023-12-23 14:10 junjie.yan Exp $
 */

@Slf4j
@Component
public class LcsFeignService {
    @Resource
    private LcsFeignClient lcsFeignClient;

    public List<LoanPlanResponse> planDetail(LoanPlanRequest request) {

        PlanDetailResponse response = null;
        String msg = "LcsFeignClient.planDetail:";
        try {
            if (Objects.isNull(request)) {
                return null;
            }
            response = lcsFeignClient.planDetail(request);
            log.info(LogUtil.clientLog("LcsFeignClient", "planDetail", request, response));
            if (Objects.isNull(response) || !"S".equals(response.getFlag()) || !"0000".equals(response.getCode()) || Objects.isNull(response.getData())) {
                msg += response == null ? "response is null" : response.getMsg();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("LcsFeignClient", "planDetail", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }
}