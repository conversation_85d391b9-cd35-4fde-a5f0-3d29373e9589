/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.rr;

import lombok.Data;

/**
 * <AUTHOR>
 * @version $ MlMetadata, v 0.1 2025-04-14 14:05 junjie.yan Exp $
 */
@Data
public class MlMetadata {
    // 用户编号.
    private Long userNo;

    // 注册时间.
    private String registerTime;

    // 最后登录时间.
    private String lastLoginTime;

    // 用户年龄.
    private Integer age;

    // 教育程度.
    private String education;

    // 收入水平.
    private String income;

    // 工作类型.
    private String jobGrade;

    // 婚姻状况.
    private String marriageStatus;

    // 用户账号状态（账号状态 0：注销 10：正常 20：临时注销 2：注销中）
    private String status;

    // 是否为飞享会员用户.
    private Boolean isVip;

    // 飞享会员续期状态.
    private Boolean renewStatus;

    // 借贷笔数.
    private Integer sucLoanOrdersNum;

    // 逾期笔数.
    private Integer overdueOrdersNum;

    // 还款笔数.
    private Integer normalOrdersNum;

    // 疑似黑产用户.
    private Boolean suspectFraud;

    // 信用现金额度.
    private Float cashCreditAmt;

    // 借记现金额度.
    private Float cashDebitAmt;

    // 工单数.
    private Integer taskQuantity;

    // 近7天会话小结数.
    private Integer summaryLogs;
}