/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.impl;

import com.xinfei.psenginecore.facade.OuterApiFacade;
import com.xinfei.psenginecore.facade.rr.request.VocKeywordsRequest;
import com.xinfei.psenginecore.facade.rr.response.BasePhpResponse;
import com.xinfei.vocprod.itl.PsengineFeinClient;
import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.util.LogUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version $ PsengineFeinClientImpl, v 0.1 2024/4/18 10:35 wancheng.qu Exp $
 */
@Component
@Slf4j
public class PsengineFeinClientImpl implements PsengineFeinClient {

    @Autowired
    private OuterApiFacade outerApiFacade;


    /**
     * @param req:
     * @return BasePhpResponse
     * <AUTHOR>
     * @description    根据im透传的用户信息流转下游psengine用来给用户打标
     * @date 2024/4/18 10:43
     */
    @Override
    public Boolean vocKeywordsSet(VocKeywordsRequest req) {
        BasePhpResponse response = new BasePhpResponse();
        String arg=null;
        try {
            arg = JsonUtil.toJson(req);
            response = outerApiFacade.vocKeywordsSet(req);
            log.info(LogUtil.clientLog("outerApiFacade", "vocKeywordsSet",arg , response));
            if (Objects.isNull(response) || (!response.isSuc())) {
                throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, ErrorLevelsEnum.ERROR);
            }
            return Boolean.TRUE;
        } catch (ClientException e) {
            log.warn(LogUtil.clientWarnLog("outerApiFacade", "vocKeywordsSet", e.getErrDtlEnum().getDescription()));
            return Boolean.FALSE;
        } catch (Exception e) {
            log.warn(LogUtil.clientErrorLog("outerApiFacade", "vocKeywordsSet", arg, response, e.getMessage()), e);
            return Boolean.FALSE;
        }
    }
}