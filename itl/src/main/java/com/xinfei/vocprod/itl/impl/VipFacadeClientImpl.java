/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.impl;

import com.xinfei.supervip.common.model.SvcBaseResponse;
import com.xinfei.supervip.interfaces.facade.admin.VipAdminOpsFacade;
import com.xinfei.supervip.interfaces.facade.admin.VipAdminQueryFacade;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundPageAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.UserSelfRefundHandleResultAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.VipOpsResultAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.dto.VipUserStatusAdminDTO;
import com.xinfei.supervip.interfaces.model.admin.request.HandleUserSelfCancelVipDeductAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.HandleUserSelfRefundImmediatelyAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.QueryUserRefundListAdminRequest;
import com.xinfei.supervip.interfaces.model.admin.request.QueryVipUserStatusAdminRequest;
import com.xinfei.vipcore.facade.*;
import com.xinfei.vipcore.facade.rr.dto.*;
import com.xinfei.vipcore.facade.rr.request.*;
import com.xinfei.vipcore.facade.rr.response.*;
import com.xinfei.vocprod.itl.CisCommonAttributes;
import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.util.LogUtil;
import com.xinfei.vocprod.util.enums.MemberTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ CisFacadeClientImpl, v 0.1 2023-12-20 14:10 junjie.yan Exp $
 */

@Component
@Slf4j
public class VipFacadeClientImpl {
    @Resource
    private TagFacade tagFacade;

    @Resource
    private VipInfoFacade vipInfoFacade;

    @Resource
    private VipAdminFacade vipAdminFacade;

    @Resource
    private VipPayFacade vipPayFacade;

    @Resource
    private TekRefundFacade tekRefundFacade;

    @Resource
    private VipAdminQueryFacade vipAdminQueryFacade;

    @Resource
    private VipAdminOpsFacade vipAdminOpsFacade;

    /**
     * 1. 是否可取消续费 can_self_close_renew，
     * 2. 是否可自助退款 can_self_refund
     * 3. 是否可取消下次扣款 can_self_cancel_refund
     * 4. 是否是飞享会员 valid_vip_identity
     *
     * @param userNo
     * @param tagKeys
     * @return
     */
    public ComputeTagResultInfoDTO computeTagResult(String userNo, List<String> tagKeys) {
        ComputeTagResultResponse response = null;
        ComputeTagResultRequest request = new ComputeTagResultRequest();
        request.setUserNo(userNo);
        request.setTagKeys(tagKeys);
        String msg = "TagFacade.computeTagResult:";
        try {
            response = tagFacade.computeTagResult(request);
            log.info(LogUtil.clientLog("TagFacade", "computeTagResult", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("TagFacade", "computeTagResult", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public VipClassifyAndIdentityDTO vipClassifyAndIdentity(String userNo) {
        Response<VipClassifyAndIdentityDTO> response = null;
        VipClassifyAndIdentityRequest request = new VipClassifyAndIdentityRequest();
        request.setUserNo(Long.parseLong(userNo));
        String msg = "VipInfoFacade.vipClassifyAndIdentity:";
        CisCommonAttributes.setVipCardCommonAttributes(request);
        try {
            response = vipInfoFacade.vipClassifyAndIdentity(request);
            log.info(LogUtil.clientLog("VipInfoFacade", "vipClassifyAndIdentity", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "vipClassifyAndIdentity", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public StrategyDecisionResultDTO decisionStrategy(String userNo, String sceneKey) {
        DecisionStrategyResponse response = null;
        DecisionStrategyRequest request = new DecisionStrategyRequest();
        request.setUserNo(userNo);
        request.setSceneKey(sceneKey);
        String msg = "TagFacade.decisionStrategy:";
        try {
            response = tagFacade.decisionStrategy(request);
            log.info(LogUtil.clientLog("TagFacade", "decisionStrategy", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("TagFacade", "decisionStrategy", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public ShowVipAdminEntryDTO showVipAdminEntry(String userNo) {
        Response<ShowVipAdminEntryDTO> response = null;
        ShowVipAdminEntryRequest request = new ShowVipAdminEntryRequest();
        request.setUserNo(Long.parseLong(userNo));
        CisCommonAttributes.setVipCardCommonAttributes(request);
        String msg = "VipAdminFacade.showVipAdminEntry:";
        try {
            response = vipAdminFacade.showVipAdminEntry(request);
            log.info(LogUtil.clientLog("VipAdminFacade", "showVipAdminEntry", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipAdminFacade", "showVipAdminEntry", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public StrategyDecisionResultListDTO batchDecisionStrategy(String userNo, List<String> sceneKeys) {
        BatchDecisionStrategyResponse response = null;
        BatchDecisionStrategyRequest request = new BatchDecisionStrategyRequest();
        request.setUserNo(userNo);
        request.setSceneKeys(sceneKeys);
        String msg = "TagFacade.batchDecisionStrategy:";
        try {
            response = tagFacade.batchDecisionStrategy(request);
            log.info(LogUtil.clientLog("TagFacade", "batchDecisionStrategy", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("TagFacade", "batchDecisionStrategy", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 判断是否飞享会员
     *
     * @param userNo
     * @return
     */
    public UserVipStatusDto userVipStatus(Long userNo) {
        UserVipStatusResponse response = null;
        UserVipStatusRequest request = new UserVipStatusRequest();
        request.setUserNo(userNo);
        String msg = "VipInfoFacade.userVipStatus:";
        try {
            response = vipInfoFacade.userVipStatus(request);
            log.info(LogUtil.clientLog("VipInfoFacade", "userVipStatus", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "userVipStatus", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 判断是否飞跃会员
     *
     * @param userNo
     * @return
     */
    public VipUserStatusAdminDTO userSuperVipStatus(Long userNo) {
        SvcBaseResponse<VipUserStatusAdminDTO> queryVipUserStatusResponse = null;
        QueryVipUserStatusAdminRequest queryVipUserStatusRequest = new QueryVipUserStatusAdminRequest();
        queryVipUserStatusRequest.setUserNo(String.valueOf(userNo));
        queryVipUserStatusRequest.setVipType(MemberTypeEnum.FEI_YUE.getCode());
        String msg = "VipInfoFacade.userSuperVipStatus:";
        try {
            queryVipUserStatusResponse = vipAdminQueryFacade.queryVipUserStatus(queryVipUserStatusRequest);

            log.info(LogUtil.clientLog("VipInfoFacade", "userSuperVipStatus", queryVipUserStatusRequest, queryVipUserStatusResponse));
            if (queryVipUserStatusResponse == null || !queryVipUserStatusResponse.isSuc()) {
                msg += queryVipUserStatusResponse == null ? "response is null" : queryVipUserStatusResponse.getMessage();
                throw new Exception(msg);
            }
            return queryVipUserStatusResponse.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "userSuperVipStatus", queryVipUserStatusRequest, queryVipUserStatusResponse, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 获取退款记录
     *
     * @param userNo
     * @return
     */
    public RefundListDto vipCardRefundList(Long userNo) {
        VipRefundListResponse response = null;
        VipRefundListByUserNoRequest request = new VipRefundListByUserNoRequest();
        request.setUserNo(userNo);
        String msg = "vipAdminFacade.refundListByUserNo:";
        try {
            response = vipAdminFacade.refundListByUserNo(request);
            log.info(LogUtil.clientLog("vipAdminFacade", "refundListByUserNo", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "refundListByUserNo", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃退款记录
     * @param userNo
     * @return
     */
    public RefundPageAdminDTO queryUserRefundList(String userNo, String vipType) {
        SvcBaseResponse<RefundPageAdminDTO> response = null;
        QueryUserRefundListAdminRequest request = new QueryUserRefundListAdminRequest();
        request.setVipType(vipType);
        request.setUserNo(userNo);
        String msg = "VipAdminQueryFacade.queryUserRefundList:";
        CisCommonAttributes.setVipCardCommonAttributes(request);
        try {
            response = vipAdminQueryFacade.queryUserRefundList(request);
            log.info(LogUtil.clientLog("VipAdminQueryFacade", "queryUserRefundList", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipAdminQueryFacade", "queryUserRefundList", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public PayAccountDTO orderPayAccount(String vipCardId, Integer cardType) {
        OrderPayAccountResponse response = null;
        OrderPayAccountRequest request = new OrderPayAccountRequest();
        request.setVipCardId(vipCardId);
        request.setCardType(cardType);
        String msg = "vipAdminFacade.orderPayAccount:";
        try {
            response = vipAdminFacade.orderPayAccount(request);
            log.info(LogUtil.clientLog("vipAdminFacade", "orderPayAccount", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "orderPayAccount", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * app自助退卡退款
     *
     * @param userNo
     * @return
     */
    public RefundApplyLastResDto refundApplyLast(Long userNo) {
        VipRefundApplyLastResponse response = null;
        VipRefundApplyRequest request = new VipRefundApplyRequest();
        request.setUserNo(userNo);
        String msg = "vipAdminFacade.refundApplyLast:";
        try {
            response = vipAdminFacade.refundApplyLast(request);
            log.info(LogUtil.clientLog("vipAdminFacade", "refundApplyLast", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "refundApplyLast", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃退款
     * @param userNo
     * @return
     */
    public UserSelfRefundHandleResultAdminDTO handleUserSelfRefundImmediately(String userNo, String vipType) {
        SvcBaseResponse<UserSelfRefundHandleResultAdminDTO> response = null;
        HandleUserSelfRefundImmediatelyAdminRequest request = new HandleUserSelfRefundImmediatelyAdminRequest();
        request.setUserNo(userNo);
        request.setVipType(vipType);
        String msg = "VipAdminOpsFacade.handleUserSelfRefundImmediately:";
        CisCommonAttributes.setVipCardCommonAttributes(request);
        try {
            response = vipAdminOpsFacade.handleUserSelfRefundImmediately(request);
            log.info(LogUtil.clientLog("VipAdminOpsFacade", "handleUserSelfRefundImmediately", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipAdminOpsFacade", "handleUserSelfRefundImmediately", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 取消自动续费
     *
     * @param userNo
     * @return
     */
    public Boolean renewStop(Long userNo) {
        RenewResponse response = null;
        RenewRequest request = new RenewRequest();
        request.setUserNo(userNo);
        String msg = "VipInfoFacade.renewStopAdmin:";
        try {
            response = vipInfoFacade.renewStopAdmin(request);
            log.info(LogUtil.clientLog("VipInfoFacade", "renewStopAdmin", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipInfoFacade", "renewStopAdmin", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 取消下一次扣款
     *
     * @param userNo
     * @return
     */
    public Boolean stopWithholdLast(Long userNo) {
        CancelWithholdResponse response = null;
        CancelWithholdRequest request = new CancelWithholdRequest();
        request.setUserNo(userNo);
        String msg = "VipPayFacade.cancelWithhold:";
        try {
            response = vipPayFacade.cancelWithhold(request);
            log.info(LogUtil.clientLog("VipPayFacade", "cancelWithhold", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipPayFacade", "cancelWithhold", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 飞跃取消下一次扣款
     * @param userNo
     * @return
     */
    public VipOpsResultAdminDTO handleUserSelfCancelVipDeduct(String userNo, String vipType) {
        SvcBaseResponse<VipOpsResultAdminDTO> response = null;
        HandleUserSelfCancelVipDeductAdminRequest request = new HandleUserSelfCancelVipDeductAdminRequest();
        request.setUserNo(userNo);
        request.setVipType(vipType);
        String msg = "VipAdminOpsFacade.handleUserSelfCancelVipDeduct:";
        CisCommonAttributes.setVipCardCommonAttributes(request);
        try {
            response = vipAdminOpsFacade.handleUserSelfCancelVipDeduct(request);
            log.info(LogUtil.clientLog("VipAdminOpsFacade", "handleUserSelfCancelVipDeduct", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("VipAdminOpsFacade", "handleUserSelfCancelVipDeduct", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public ShowRefundEntryDTO showRefundEntry(Long userNo) {
        Response<ShowRefundEntryDTO> response = null;
        ShowRefundEntryRequest request = new ShowRefundEntryRequest();
        request.setUserNo(userNo);
        String msg = "TekRefundFacade.showRefundEntry:";
        try {
            response = tekRefundFacade.showRefundEntry(request);
            log.info(LogUtil.clientLog("TekRefundFacade", "showRefundEntry", request, response));
            if (response == null || !response.isSuc() || !"1".equals(response.getCode()) || !"success".equals(response.getMessage())) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("TekRefundFacade", "showRefundEntry", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    /**
     * 根据用户号判断用户分流
     */
    public VipClassifyTypeDTO vipClassifyGroup(Long userNo) {
        Response<VipClassifyTypeDTO> response = null;
        VipClassifyTypeRequest request = new VipClassifyTypeRequest();
        request.setUserNo(userNo);
        String msg = "vipAdminFacade.vipClassifyGroup:";
        try {
            response = vipInfoFacade.vipClassifyType(request);
            log.info(LogUtil.clientLog("vipAdminFacade", "vipClassifyGroup", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "vipClassifyGroup", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }

    public VipClassifyTypeDTO vipClassifyType(Long userNo) {
        Response<VipClassifyTypeDTO> response = null;
        VipClassifyTypeRequest request = new VipClassifyTypeRequest();
        request.setUserNo(userNo);
        String msg = "vipAdminFacade.vipClassifyType:";
        try {
            response = vipInfoFacade.vipClassifyType(request);
            log.info(LogUtil.clientLog("vipAdminFacade", "vipClassifyType", request, response));
            if (response == null || !response.isSuc()) {
                msg += response == null ? "response is null" : response.getMessage();
                throw new Exception(msg);
            }
            return response.getData();
        } catch (Exception e) {
            log.error(LogUtil.clientErrorLog("vipAdminFacade", "vipClassifyType", request, response, msg), e);
            throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, msg, ErrorLevelsEnum.ERROR);
        }
    }
}