/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2024. All Rights Reserved.
 */

package com.xinfei.vocprod.itl;

import com.xinfei.vocprod.itl.rr.GueryGuessReq;
import com.xinfei.vocprod.itl.rr.GueryGuessResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @version $ AmsFeignClient, v 0.1 2024/8/12 11:28 you.zhang Exp $
 */
@FeignClient(name = Constants.DMX, contextId = Constants.DMX + ".FeinClient", path = "/")
public interface DmxFeignClient {

    @PostMapping("/v1/core/ml/queryGuess")
    GueryGuessResp queryGuess(GueryGuessReq req, @RequestHeader(name = "source") String source);

}
