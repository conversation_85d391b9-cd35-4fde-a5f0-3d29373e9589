/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 账单详情DTO
 *
 * <AUTHOR>
 * @version $ BillDetailDto, v 0.1 2025/1/2 14:30 shaohui.chen Exp $
 */
@Data
public class BillDetailDto {

    @ApiModelProperty("期数ID")
    private String periodId;

    @ApiModelProperty("期数名称（首期、2期、3期等）")
    private String periodName;

    @ApiModelProperty("还款日期")
    private LocalDate repaymentDate;

    @ApiModelProperty("应还金额")
    private BigDecimal amount;

    @ApiModelProperty("账单状态：已还、逾期、未到期")
    private String status;

    @ApiModelProperty("订单号")
    private String orderNumber;

    @ApiModelProperty("是否可点击")
    private Boolean clickable;

    @ApiModelProperty("逾期天数（仅逾期状态有值）")
    private Integer overdueDays;

    @ApiModelProperty("逾期费用（仅逾期状态有值）")
    private BigDecimal overdueFee;
}
