package com.xinfei.vocprod.itl;

import com.xinfei.vocprod.itl.rr.NPayResponse;
import com.xinfei.vocprod.itl.rr.PublicAccountInfo;
import com.xinfei.vocprod.itl.rr.PublicAccountRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;


/**
 * <AUTHOR>
 * @version $ NPayFeignClient, v 0.1 2025/03/21 15:28 pengming.liu Exp $
 */
@FeignClient(name = Constants.N_PAY, contextId = Constants.N_PAY + ".FeinClient", path = "/")
public interface NPayFeignClient {

    /**
     * 根据userNo查询对公账户信息
     * 接口文档：https://alidocs.dingtalk.com/i/nodes/qnYMoO1rWxDKGZb2IMvyr60MW47Z3je9?doc_type=wiki_doc&utm_medium=dingdoc_doc_plugin_card&utm_scene=team_space&utm_source=dingdoc_doc
     *
     * @param request
     * @return
     */
    @PostMapping("/bank/query-public-account-info")
    NPayResponse<PublicAccountInfo> queryPublicAccountInfo(PublicAccountRequest request);
}
