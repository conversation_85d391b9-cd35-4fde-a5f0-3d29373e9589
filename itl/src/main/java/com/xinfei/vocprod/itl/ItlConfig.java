/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.itl;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 整合类 config
 *
 * <AUTHOR>
 * @version $ Constants, v 0.1 2023/8/29 09:54 Chengsheng.Li Exp $
 */
@Getter
@Configuration
@EnableFeignClients(basePackageClasses = {ItlConfig.class})
@ComponentScan(basePackageClasses = {ItlConfig.class})
public class ItlConfig {

    /*@Value("${udesk.url.base:jmx_cash}")
    private String urlBase;

    @Value("${udesk.public.key:jmx_cash}")
    private String upbKey;

    @Value("${udesk.im.userKey:jmx_cash}")
    private String imUserKey;*/
}
