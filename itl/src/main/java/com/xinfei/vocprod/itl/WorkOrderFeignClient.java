package com.xinfei.vocprod.itl;

import com.xinfei.vocprod.itl.rr.CreateTaskDto;
import com.xinfei.vocprod.itl.rr.GetTaskByUserIdReq;
import com.xinfei.vocprod.itl.rr.GetTaskByUserIdResp;
import com.xinfei.vocprod.itl.rr.WorkOrderResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version $ WorkOrderFeignClient, v 0.1 2024/1/15 15:28 qu.lu Exp $
 */
@FeignClient(name = Constants.WORK_ORDER, contextId = Constants.WORK_ORDER + ".PhpFeinClient", path = "/")
public interface WorkOrderFeignClient {
    @PostMapping("/outapi/task/create-task")
    WorkOrderResponse<Object> createTask(CreateTaskDto request);

    @PostMapping("/outapi/task/get-task-by-user-id")
    WorkOrderResponse<GetTaskByUserIdResp> getTaskByUserId(GetTaskByUserIdReq request);
}
