/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.itl.impl;

import com.xinfei.cashiercore.common.service.facade.api.ManagementFacade;
import com.xinfei.cashiercore.common.service.facade.request.management.WhitelistMarkRequest;
import com.xinfei.cashiercore.common.service.facade.response.DataResponse;
import com.xinfei.cashiercore.common.service.facade.vo.BaseVO;
import com.xinfei.vocprod.itl.CashiercoreFeignClient;
import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.util.LogUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ CashiercoreFeignClientImpl, v 0.1 2024/8/5 14:02 wancheng.qu Exp $
 */
@Component
@Slf4j
public class CashiercoreFeignClientImpl implements CashiercoreFeignClient {

    @Autowired
    private ManagementFacade managementFacade;

    @Override
    public Boolean vocKeywordsSet(WhitelistMarkRequest req) {
        DataResponse<BaseVO> response = new DataResponse<>();
        String arg = null;
        try {
            arg = JsonUtil.toJson(req);
            response = managementFacade.whitelistMark(req);
            log.info(LogUtil.clientLog("managementFacade", "whitelistMark", arg, response));
            if (Objects.isNull(response) || (!response.isSuccess())) {
                throw new ClientException(FeignErrDtlEnum.REQ_PARAM_NOT_VALID, ErrorLevelsEnum.ERROR);
            }
            return Boolean.TRUE;
        } catch (ClientException e) {
            log.warn(LogUtil.clientWarnLog("managementFacade", "whitelistMark", e.getErrDtlEnum().getDescription()));
            return Boolean.FALSE;
        } catch (Exception e) {
            log.warn(LogUtil.clientErrorLog("managementFacade", "whitelistMark", arg, response, e.getMessage()), e);
            return Boolean.FALSE;
        }
    }
}