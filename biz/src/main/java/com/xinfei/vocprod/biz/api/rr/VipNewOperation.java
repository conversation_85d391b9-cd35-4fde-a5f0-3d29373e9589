/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ VipOperation, v 0.1 2024-10-31 19:38 junjie.yan Exp $
 */
@Data
public class VipNewOperation {

    @ApiModelProperty(value = "客服大厅自助退款")
    private Boolean kfCanSelfRefund;
    @ApiModelProperty(value = "客服大厅自助关闭续费")
    private Boolean kfCanSelfCloseRenew;
    @ApiModelProperty(value = "客服大厅自助取消扣款")
    private Boolean kfCanSelfCancelWithhold;
    @ApiModelProperty(value = "客服大厅退款挽留")
    private Boolean kfSelfRefundRetain;


}