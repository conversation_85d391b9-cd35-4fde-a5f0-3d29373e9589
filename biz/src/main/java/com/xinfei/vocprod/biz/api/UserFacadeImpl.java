/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.common.TechplayConstants;
import com.xinfei.vocprod.biz.log.DigestLogAnnotated;
import com.xinfei.vocprod.biz.log.TradeDigestLog;
import com.xinfei.vocprod.biz.service.UserService;
import com.xinfei.vocprod.biz.template.TradeCallBack;
import com.xinfei.vocprod.biz.template.TradeTemplate;
import com.xinfei.vocprod.biz.util.ParamCheckUtil;
import com.xinfei.vocprod.facade.UserFacade;
import com.xinfei.vocprod.facade.rr.ScreenshotConfineRequest;
import com.xinfei.vocprod.facade.rr.ScreenshotConfineResponse;
import com.xinfei.vocprod.facade.rr.TestResponse;
import com.xinfei.vocprod.facade.rr.UserRequest;
import com.xinfei.vocprod.facade.rr.UserResponse;
import com.xinfei.vocprod.itl.impl.VocmngFeignClientImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_EMAIL;
import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_NAME;
import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_REQUEST;
import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_REQUEST_USER;

/**
 * 用户信息远程接口默认实现类<br/>
 *
 * <AUTHOR>
 * @version $ UserFacadeImpl, v 0.1 2023/8/28 12:20 Chengsheng.Li Exp $
 */
@RestController
@RequestMapping("/user")
public class UserFacadeImpl implements UserFacade {

    @Autowired
    private UserService userService;

    @Resource
    private VocmngFeignClientImpl vocmngFeignClientImpl;

    @Override
    @PostMapping("/addOrEdit")
    @DigestLogAnnotated(TechplayConstants.TRADE_DIGEST_LOG)
    public UserResponse addOrEdit(@RequestBody UserRequest request) {

        final UserResponse result = new UserResponse();

        TradeTemplate.trade(result, new TradeCallBack() {

            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUser(), USER_REQUEST_USER);
                ParamCheckUtil.checkParamNotBlank(request.getUser().getName(), USER_NAME);
                ParamCheckUtil.checkParamNotBlank(request.getUser().getEmail(), USER_EMAIL);
            }

            @Override
            public void doTrade() {
                userService.addOrEdit(request.getUser());
                result.setId(request.getUser().getId());
            }

            public TradeDigestLog buildDigestLog() {
                return new TradeDigestLog(request);
            }

        });

        return result;
    }
    @Override
    @PostMapping("/getScreenshotConfine")
    @DigestLogAnnotated(TechplayConstants.TRADE_DIGEST_LOG)
    public ScreenshotConfineResponse getScreenshotConfine(@RequestBody ScreenshotConfineRequest request) {
        final ScreenshotConfineResponse result = new ScreenshotConfineResponse();
        TradeTemplate.trade(result, new TradeCallBack() {
                public void checkParameter () {
                    ParamCheckUtil.checkParamNotNull(request.getUserNo(), "userNo");
                    ParamCheckUtil.checkParamNotNull(request.getAppName(), "appNmae");

                }
            @Override
            public void doTrade() {
                result.setSuc(Boolean.TRUE);
                result.setData(vocmngFeignClientImpl.getScreenshotConfine(request));
            }

            public TradeDigestLog buildDigestLog() {
                return new TradeDigestLog(request.getAppName());
            }
        });

        return result;
    }

    /*
     * 此接口仅仅是用来做性能测试使用
     * */
    @RequestMapping("/test")
    @DigestLogAnnotated(TechplayConstants.TRADE_DIGEST_LOG)
    public TestResponse test(@RequestParam("t") int t) {

        final TestResponse result = new TestResponse();

        TradeTemplate.trade(result, new TradeCallBack() {

            public void checkParameter() {

            }

            @Override
            public void doTrade() {
                result.setTime(userService.test(t));
            }

            public TradeDigestLog buildDigestLog() {
                return new TradeDigestLog("test");
            }

        });

        return result;
    }
}
