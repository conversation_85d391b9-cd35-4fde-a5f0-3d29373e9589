/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.service.impl;

import com.xinfei.vocprod.biz.api.rr.BillingSubmitRequest;
import com.xinfei.vocprod.itl.impl.WorkOrderFeignClientImpl;
import com.xinfei.vocprod.itl.rr.CreateTaskDto;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ BillServiceImpl, v 0.1 2024-11-14 17:01 junjie.yan Exp $
 */
@Service
public class BillServiceImpl {

    @Resource
    private WorkOrderFeignClientImpl workOrderFeignClient;

    @Value("${bill.submit.taskType}")
    private Integer taskType;

    @Value("${bill.submit.subTaskType}")
    private Integer subTaskType;

    public Boolean billingSubmit(BillingSubmitRequest request) {
        CreateTaskDto createTaskDto = new CreateTaskDto();
        createTaskDto.setIsCustomize(true);
        createTaskDto.setCustomizeSceneId(taskType);
        createTaskDto.setCustomizeChannel(subTaskType);
        createTaskDto.setTaskTypeId(subTaskType);
        createTaskDto.setQuestionTypeId(0);
        createTaskDto.setEmergencyStatus(1);

        //订单号字段值传身份证，工单模板中会展示字段中文名为身份证
        createTaskDto.setRefundAmount(request.getIdCardNumber());
        createTaskDto.setAppId("xyf01");
        createTaskDto.setCallNumber("13312341234");
        createTaskDto.setDevice(request.getMobile());
        createTaskDto.setVipOrderNumber(request.getName());
        createTaskDto.setComment("邮箱：" + request.getEmail() + " 具体诉求：" + request.getAppeal());

        workOrderFeignClient.createTask(createTaskDto);
        return true;
    }

}