package com.xinfei.vocprod.biz.service;

import com.xinfei.vocprod.facade.request.VocKeyRequest;
import com.xinfei.vocprod.facade.response.VocKeyResponse;
import com.xinfei.vocprod.itl.rr.UdeskSendMessageRequest;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
public interface PsengineService {
    VocKeyResponse vocKeywordsSet(VocKeyRequest req);

    VocKeyResponse queryPublicAccountInfo(VocKeyRequest req);

    /**
     * 查询用户状态
     */
    VocKeyResponse getUserStatus(VocKeyRequest req);

    /**
     * 查询账单相关
     */
    VocKeyResponse queryIVROrderList(VocKeyRequest req);

    /**
     * 校验用户
     */
    VocKeyResponse verifyIdCardLast6(VocKeyRequest req);

    /**
     * 查询用户会员状态
     */
    VocKeyResponse queryUserVipStatus(VocKeyRequest req);

    /**
     * 查询用户会员分群
     */
    VocKeyResponse queryVipClassifyGroup(VocKeyRequest req);

    /**
     * 查询IVR最新账单信息
     */
    VocKeyResponse queryIVRLatestBill(VocKeyRequest req);

    /**
     * 查询IVR当月应还金额
     */
    VocKeyResponse queryIVRMonthlyAmount(VocKeyRequest req);

    /**
     * 查询IVR在贷未结清信息
     */
    VocKeyResponse queryIVRLoanSettlement(VocKeyRequest req);

    /**
     * 发送短信
     */
    void sendSms(UdeskSendMessageRequest req);

    /**
     * 查询会员卡列表
     */
    VocKeyResponse queryMembershipCards(VocKeyRequest req);

    /**
     * 查询用户账单详情
     */
    VocKeyResponse queryUserBillDetail(VocKeyRequest req);
}
