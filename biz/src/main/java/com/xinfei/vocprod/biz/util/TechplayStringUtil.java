/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.util;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version $ TechplayStringUtil, v 0.1 2023/8/28 21:01 <PERSON>yan.Huang Exp $
 */
public final class TechplayStringUtil {

    /**
     * 用默认字符串替换空字符串
     *
     * @param   str           被检查的字符串
     * @param   defaultStr    默认字符串
     * @return  用默认值处理后的字符串
     */
    public static String defaultIfBlank(String str, String defaultStr){

        if(StringUtils.hasText(str)){
            return str;
        }

        return defaultStr;
    }
}
