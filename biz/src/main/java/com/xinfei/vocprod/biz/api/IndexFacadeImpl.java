/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.api.rr.ConfigResponse;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskLinkRequest;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskResultDto;
import com.xinfei.vocprod.biz.api.rr.VipRequest;
import com.xinfei.vocprod.biz.common.TechplayConstants;
import com.xinfei.vocprod.biz.log.DigestLogAnnotated;
import com.xinfei.vocprod.biz.log.QueryDigestLog;
import com.xinfei.vocprod.biz.service.IndexService;
import com.xinfei.vocprod.biz.template.QueryCallBack;
import com.xinfei.vocprod.biz.template.QueryTemplate;
import com.xinfei.vocprod.biz.util.ParamCheckUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_REQUEST;
import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_REQUEST_USER;

@RestController
@RequestMapping("/index")
@Slf4j
public class IndexFacadeImpl {

    @Autowired
    private IndexService indexService;

    @PostMapping("/config")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public ConfigResponse config(@RequestBody VipRequest request) {
        final ConfigResponse result = new ConfigResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                Map<String, Object> configs = indexService.config(request.getUserNo());
                log.info("config traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), configs);
                result.setSuc(Boolean.TRUE);
                result.setConfigs(configs);
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }

    @PostMapping("/faqConfig")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public ConfigResponse faqConfig(@RequestBody VipRequest request) {
        final ConfigResponse result = new ConfigResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                Map<String, Object> configs = indexService.faqConfig(request.getUserNo());
                log.info("faqConfig traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), configs);
                result.setSuc(Boolean.TRUE);
                result.setConfigs(configs);
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }

    @PostMapping("/getVipUDeskLink")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public HelpCenterGetUdeskResultDto getVipUDeskLink(@RequestBody HelpCenterGetUdeskLinkRequest request) {
        final HelpCenterGetUdeskResultDto result = new HelpCenterGetUdeskResultDto();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                HelpCenterGetUdeskResultDto resp = indexService.getUdeskLink(request);
                log.info("getVipUDeskLink,req:{},result:{}", JsonUtil.toJson(request), resp);
                result.setSuc(Boolean.TRUE);
                result.setUrl(resp.getUrl());
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }


}
