/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.log;

/**
 * 摘要日志线程变量
 *
 * <AUTHOR>
 * @version $ DigestLogHolder, v 0.1 2023/8/28 20:35 <PERSON><PERSON>.Huang Exp $
 */
public class DigestLogHolder {

    /** 摘要日志线程变量 */
    private static final ThreadLocal<BaseDigestLog> DIGEST_LOGGER = new ThreadLocal<BaseDigestLog>();

    /**
     * 设置日志对象
     *
     * @param BaseDigestLog 日志对象
     */
    public static void set(BaseDigestLog BaseDigestLog) {
        if (DIGEST_LOGGER.get() == null) {
            DIGEST_LOGGER.set(BaseDigestLog);
        }
    }

    /**
     * 获取日志对象
     * @return  日志对象
     */
    public static BaseDigestLog get() {
        return DIGEST_LOGGER.get();
    }

    /**
     * 清理线程变量
     */
    public static void clear() {
        DIGEST_LOGGER.set(null);
    }
}
