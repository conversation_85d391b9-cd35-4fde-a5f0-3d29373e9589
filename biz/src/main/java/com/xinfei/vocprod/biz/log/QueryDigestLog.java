/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.log;

import com.xinfei.vocprod.facade.rr.TeamQueryRequest;
import lombok.Setter;

import java.util.Map;

/**
 * 查询摘要日志
 *
 * <AUTHOR>
 * @version $ QueryDigestLog, v 0.1 2023/8/28 20:32 <PERSON>yan.Huang Exp $
 */
@Setter
final public class QueryDigestLog extends BaseDigestLog {

    /** 团队编码 */
    private String teamCode;

    /** 团队编码 */
    private String appName;

    /**
     * 根据团队信息查询请求构造器日志对象
     *
     * @param teamQueryRequest 团队信息查询请求
     */
    public QueryDigestLog(TeamQueryRequest teamQueryRequest) {

        if (teamQueryRequest == null) {
            return;
        }

        this.teamCode = teamQueryRequest.getTeamCode();
    }

    public QueryDigestLog(String appName) {

        this.appName = appName;
    }

    /**
     * 日志业务信息组装
     *
     * @param log 日志信息
     */
    final protected void composeBizInfo(Map<String,String> log) {
        log.put("teamCode", teamCode);
        log.put("appName", appName);
    }

}
