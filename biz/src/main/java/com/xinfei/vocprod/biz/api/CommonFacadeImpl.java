/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.common.TechplayConstants;
import com.xinfei.vocprod.biz.log.DigestLogAnnotated;
import com.xinfei.vocprod.biz.log.QueryDigestLog;
import com.xinfei.vocprod.biz.service.CommonService;
import com.xinfei.vocprod.biz.template.QueryCallBack;
import com.xinfei.vocprod.biz.template.QueryTemplate;
import com.xinfei.vocprod.biz.util.ParamCheckUtil;
import com.xinfei.vocprod.facade.rr.GetABRequest;
import com.xinfei.vocprod.facade.rr.GetABResponse;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_REQUEST;
import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_REQUEST_USER;

@RestController
@RequestMapping("/common")
@Slf4j
public class CommonFacadeImpl {

    @Autowired
    private CommonService commonService;


    @PostMapping("/getGroupAB")
    @DigestLogAnnotated(TechplayConstants.TRADE_DIGEST_LOG)
    public GetABResponse getGroupAB(@Validated @RequestBody GetABRequest request) {

        final GetABResponse result = new GetABResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
                ParamCheckUtil.checkParamNotNull(request.getBizKey(), "bizKey");
            }

            @Override
            public void doQuery() {
                String groupAB = commonService.getGroupAB(request);
                log.info("getGroupAB req:{},result:{}", JsonUtil.toJson(request), groupAB);
                result.setSuc(Boolean.TRUE);
                result.setGroup(groupAB);
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }


}
