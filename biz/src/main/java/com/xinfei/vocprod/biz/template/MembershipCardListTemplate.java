/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import com.xinfei.vocprod.itl.rr.MembershipCardDto;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 会员卡列表HTML模板
 *
 * <AUTHOR>
 * @version $ MembershipCardListTemplate, v 0.1 2025/1/2 14:30 shaohui.chen Exp $
 */
@Component
public class MembershipCardListTemplate implements HtmlTemplate {

    @Override
    public String render(Object data, Map<String, Object> config) {
        @SuppressWarnings("unchecked")
        List<MembershipCardDto> membershipCards = (List<MembershipCardDto>) data;
        
        StringBuilder html = new StringBuilder();
        
        // 获取配置参数
        boolean showSelectButton = (Boolean) config.getOrDefault("showSelectButton", true);
        String cardStyle = (String) config.getOrDefault("cardStyle", "default");
        
        for (MembershipCardDto card : membershipCards) {
            html.append(buildCardItem(card, showSelectButton, cardStyle));
        }
        
        return html.toString();
    }

    @Override
    public String getTemplateName() {
        return "会员卡列表";
    }

    @Override
    public Class<?> getSupportedDataType() {
        return List.class;
    }

    /**
     * 构建单个会员卡项HTML
     */
    private String buildCardItem(MembershipCardDto card, boolean showSelectButton, String cardStyle) {
        String statusColor = getStatusColor(card.getStatus());
        String buttonHtml = showSelectButton ?
            "<button style='background-color: #007AFF; color: white; border: none; padding: 6px 16px; border-radius: 4px; font-size: 12px;'>选择</button>" : "";

        // 格式化日期，处理null值
        String validStartDate = card.getValidStartDate() != null ? card.getValidStartDate().toString() : "";
        String validEndDate = card.getValidEndDate() != null ? card.getValidEndDate().toString() : "";
        String orderTime = card.getOrderTime() != null ?
            card.getOrderTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")) : "";

        return String.format(
            "<div style=\"display: flex; margin: 0 20px; padding: 15px 0; border-bottom: 1px solid #E5E5E5;\">" +
            "    <div style=\"flex: 1; display: flex; flex-direction: column; justify-content: center;\">" +
            "        <div style=\"font-size: 16px; font-weight: bold; color: #333; margin-bottom: 8px;\">%s</div>" +
            "        <div style=\"font-size: 12px; color: #999; margin-bottom: 4px;\">有效期：%s至%s</div>" +
            "        <div style=\"font-size: 12px; color: #999;\">下单时间：%s</div>" +
            "    </div>" +
            "    <div style=\"flex: 0 0 auto; display: flex; flex-direction: column; align-items: flex-end; justify-content: center;\">" +
            "        <div style=\"font-size: 18px; font-weight: bold; color: #FF6B35; margin-bottom: 8px;\">实付￥%.2f</div>" +
            "        <div style=\"background-color: %s; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;\">%s</div>" +
            "        <div style=\"margin-top: 8px;\">%s</div>" +
            "    </div>" +
            "</div>",
            card.getCardName() != null ? card.getCardName() : "",
            validStartDate,
            validEndDate,
            orderTime,
            card.getActualAmount() != null ? card.getActualAmount() : 0.0,
            statusColor,
            card.getStatus() != null ? card.getStatus() : "",
            buttonHtml
        );
    }

    /**
     * 获取状态颜色
     */
    private String getStatusColor(String status) {
        switch (status) {
            case "状态":
                return "#28A745";
            case "生效中":
                return "#007AFF";
            case "已结束":
                return "#6C757D";
            default:
                return "#6C757D";
        }
    }
}
