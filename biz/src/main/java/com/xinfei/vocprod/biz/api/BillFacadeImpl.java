/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.api.rr.BillingSubmitRequest;
import com.xinfei.vocprod.biz.api.rr.BillingSubmitResponse;
import com.xinfei.vocprod.biz.common.TechplayConstants;
import com.xinfei.vocprod.biz.log.DigestLogAnnotated;
import com.xinfei.vocprod.biz.log.QueryDigestLog;
import com.xinfei.vocprod.biz.service.impl.BillServiceImpl;
import com.xinfei.vocprod.biz.template.QueryCallBack;
import com.xinfei.vocprod.biz.template.QueryTemplate;
import com.xinfei.vocprod.biz.util.ParamCheckUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_REQUEST;

@RestController
@RequestMapping("/bill")
@Slf4j
public class BillFacadeImpl {

    @Autowired
    private BillServiceImpl billService;

    @PostMapping("/billingSubmit")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public BillingSubmitResponse billSubmit(@Validated @RequestBody BillingSubmitRequest request) {

        final BillingSubmitResponse result = new BillingSubmitResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
            }

            @Override
            public void doQuery() {
                Boolean resp = billService.billingSubmit(request);
                log.info("billSubmit req:{},result:{}", JsonUtil.toJson(request), resp);
                result.setResult(resp);
                result.setSuc(Boolean.TRUE);
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }
}
