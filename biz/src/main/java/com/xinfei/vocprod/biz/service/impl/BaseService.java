/**
 * Xinfei.com Inc.
 * <p>
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.service.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/**
 * 通用增删改查基类
 *
 * <AUTHOR>
 * @version $ TeamServiceImpl, v 0.1 2023/8/29 09:02 Chengsheng.Li Exp $
 */
public abstract class BaseService<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> {
    private static Logger log = LoggerFactory.getLogger(BaseService.class);
}
