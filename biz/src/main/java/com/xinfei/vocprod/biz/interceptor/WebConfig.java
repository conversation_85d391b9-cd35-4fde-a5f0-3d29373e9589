package com.xinfei.vocprod.biz.interceptor;

import apollo.com.google.common.collect.Lists;
import com.xinfei.vocprod.util.interceptor.GateInterceptor;
import com.xinfei.vocprod.util.interceptor.TraceInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ WebConfig, v 0.1 2025/3/19 16:53 shaohui.chen Exp $
 */
@Configuration
@Slf4j
public class WebConfig implements WebMvcConfigurer {
    @Resource
    private TraceInterceptor traceInterceptor;

    @Resource
    private GateInterceptor gateInterceptor;


    /**
     * 健康检查、swagger、error页面排除
     */
    public static List<String> COMMON_EXCLUDE_PATH_PATTERNS =
            Lists.newArrayList(
                    "/v2/api-docs/**",
                    "/static/**",
                    "/webjars/**",
                    "/favicon.ico",
                    "/doc.html",
                    "/actuator/**",
                    "/swagger-resources/**",
                    "/swagger-ui/**",
                    "/error");



    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(traceInterceptor).excludePathPatterns(COMMON_EXCLUDE_PATH_PATTERNS);
        registry.addInterceptor(gateInterceptor).excludePathPatterns(COMMON_EXCLUDE_PATH_PATTERNS);
    }


}
