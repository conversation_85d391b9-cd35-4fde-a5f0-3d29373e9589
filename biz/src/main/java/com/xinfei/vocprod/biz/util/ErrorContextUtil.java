/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.util;

import com.xinfei.vocprod.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocprod.biz.model.enums.TechplayErrScenarioEnum;
import com.xinfei.vocprod.biz.model.exception.TechplayException;
import com.xinfei.xfframework.common.ErrorContext;

/**
 * 标准错误码工具
 * <p>在标准错误码的位置如下：
 *      <table border="1">
 *      <tr>
 *      <td>位置</td><td>1</td><td>2</td><td>3</td><td bgcolor="yellow">4</td><td>5</td><td>6</td><td>7</td><td>8</td><td>9</td><td bgcolor="red">10</td><td bgcolor="red">11</td><td bgcolor="red">12</td>
 *      </tr>
 *      <tr>
 *      <td>示例</td><td>X</td><td>E</td><td>0</td><td>1</td><td>0</td><td>1</td><td>0</td><td>1</td><td>1</td><td>0</td><td>2</td><td>7</td>
 *      </tr>
 *      <tr>
 *      <td>说明</td><td colspan=2>固定<br>标识</td><td>规<br>范<br>版<br>本</td><td>错<br>误<br>级<br>别</td><td>错<br>误<br>类<br>型</td><td colspan=4>错误场景</td><td colspan=3>错误编<br>码</td>
 *      </tr>
 *      </table>
 *
 * <AUTHOR>
 * @version $ ErrorContextUtil, v 0.1 2023/8/28 18:59 Jinyan.Huang Exp $
 */
public final class ErrorContextUtil {

    /** 错误码前缀，信飞全站统一 */
    private final static String ERR_CODE_PREFIX="XE";

    /** 错误码版本，信飞全站统一 */
    private final static String ERR_CODE_VERSION="0";

    /** 错误码类型，信飞全站统一 */
    private final static String ERR_CODE_TYPE="0";

    /**
     * 生成标准错误码
     *
     * @param scenario      错误场景
     * @param exception     交易异常
     * @return              错误上下文
     */
    public static ErrorContext genErrorContext(TechplayErrScenarioEnum scenario,
                                               TechplayException exception) {

        TechplayErrDtlEnum detail = exception.getResultCodeEnum();

        // 场景说明
        String desc = scenario.getDescription();
        // 错误码说明
        desc = desc + "/" + detail.getDescription();
        // 错误信息
        desc = desc + "/" + exception.getMessage();

        return genErrorContext(scenario, detail, desc);
    }

    /**
     * 生成标准错误码
     *
     * @param scenario  错误场景
     * @param detail    错误明细
     * @param message   错误信息
     * @return          错误上下文
     */
    public static ErrorContext genErrorContext(TechplayErrScenarioEnum scenario,
                                               TechplayErrDtlEnum detail, String message) {

        // 信飞统一前缀：2位
        String codeStr = ERR_CODE_PREFIX;
        // 错误码版本：1位
        codeStr = codeStr + ERR_CODE_VERSION;
        // 错误码级别：1位
        codeStr = codeStr + detail.getErrorLevel().getCode();
        // 错误码类型：1位
        codeStr = codeStr + ERR_CODE_TYPE;
        // 场景编码：4位
        codeStr = codeStr + scenario.getCode();
        // 错误码：3位
        codeStr = codeStr + detail.getCode();

        ErrorContext errorContext = new ErrorContext();
        errorContext.setErrCode(codeStr);
        errorContext.setErrDesc(message);

        return errorContext;
    }
}
