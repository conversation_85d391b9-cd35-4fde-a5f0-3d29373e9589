/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ VipOperation, v 0.1 2024-10-31 19:38 junjie.yan Exp $
 */
@Data
public class VipOperation {

    @ApiModelProperty(value = "是否可取消下次扣款")
    private Boolean canSelfCancelWithhold;
    @ApiModelProperty(value = "是否退款")
    private Boolean canRefund;
    @ApiModelProperty(value = "是否可自助退款")
    private Boolean canSelfRefund;
    @ApiModelProperty(value = "是否可取消续费")
    private Boolean canSelfCloseRenew;


}