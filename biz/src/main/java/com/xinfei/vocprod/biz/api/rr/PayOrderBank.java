/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ PayOrderBank, v 0.1 2024-11-12 17:57 junjie.yan Exp $
 */
@Data
public class PayOrderBank {

    @ApiModelProperty("银行id")
    private String bankId;
    @ApiModelProperty("银行名称")
    private String bankName;
    @ApiModelProperty("支付订单号")
    private String payOrderNo;
    @ApiModelProperty("银行卡号后4位")
    private String tailBankNo;

}