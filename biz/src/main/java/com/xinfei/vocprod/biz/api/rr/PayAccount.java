/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ PayAccount, v 0.1 2024-11-12 17:57 junjie.yan Exp $
 */
@Data
public class PayAccount {

    @ApiModelProperty("银行卡信息")
    private PayOrderBank payOrderBankDTO;
    @ApiModelProperty("支付宝账号")
    private String aliAccount;
    @ApiModelProperty("退款账号类型：1，银行卡，2：支付宝")
    private Integer refundAccountType;

}