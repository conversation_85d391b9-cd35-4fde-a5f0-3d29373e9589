/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import com.xinfei.vocprod.itl.rr.BillDetailDto;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 账单时间轴HTML模板
 *
 * <AUTHOR>
 * @version $ BillTimelineTemplate, v 0.1 2024-12-20 AI Exp $
 */
public class BillTimelineTemplate implements HtmlTemplate {

    @Override
    public String render(Object data, Map<String, Object> config) {
        @SuppressWarnings("unchecked")
        List<BillDetailDto> billDetails = (List<BillDetailDto>) data;
        
        StringBuilder html = new StringBuilder();
        
        // 获取配置参数
        String backgroundColor = (String) config.getOrDefault("backgroundColor", "#f8f9fa");
        String padding = (String) config.getOrDefault("padding", "10px 20px");
        boolean showTimeline = (Boolean) config.getOrDefault("showTimeline", true);
        
        html.append(String.format("<div style='padding: %s; background-color: %s;'>", padding, backgroundColor));
        
        for (int i = 0; i < billDetails.size(); i++) {
            BillDetailDto bill = billDetails.get(i);
            String statusColor = getStatusColor(bill.getStatus());
            boolean isLast = (i == billDetails.size() - 1);
            
            html.append(buildBillItem(bill, statusColor, isLast && showTimeline));
        }
        
        html.append("</div>");
        return html.toString();
    }

    @Override
    public String getTemplateName() {
        return "账单时间轴";
    }

    @Override
    public Class<?> getSupportedDataType() {
        return List.class;
    }

    /**
     * 构建单个账单项HTML
     */
    private String buildBillItem(BillDetailDto bill, String statusColor, boolean isLast) {
        return String.format(
            "<div style='display: flex; align-items: center; padding: 8px 0; position: relative;'>" +
            "    <div style='flex: 0 0 50px; text-align: left; margin-right: 20px; position: relative;'>" +
            "        <div style='font-size: 14px; color: #333; margin-bottom: 2px;'>%s</div>" +
            "        <div style='font-size: 12px; color: #999;'>%s</div>" +
            "        <div style='position: absolute; right: -10px; top: 50%%; transform: translateY(-50%%); width: 8px; height: 8px; border-radius: 50%%; background-color: #E5E5E5; border: 2px solid #fff; z-index: 2;'></div>" +
            (isLast ? "" : "        <div style='position: absolute; right: -6px; top: 50%%; width: 2px; height: 40px; background-color: #E5E5E5; z-index: 1;'></div>") +
            "    </div>" +
            "    <div style='flex: 1; margin-left: 10px;'>" +
            "        <span style='font-size: 16px; font-weight: bold; color: #333; margin-right: 20px;'>¥%.2f</span>" +
            "        <span style='font-size: 14px; color: %s;'>%s</span>" +
            "    </div>" +
            "</div>",
            bill.getPeriodName(),
            bill.getRepaymentDate().format(DateTimeFormatter.ofPattern("MM/dd")),
            bill.getAmount(),
            statusColor,
            bill.getStatus()
        );
    }

    /**
     * 获取状态颜色
     */
    private String getStatusColor(String status) {
        switch (status) {
            case "已还":
                return "#28A745";
            case "逾期":
                return "#DC3545";
            case "未到期":
                return "#999999";
            default:
                return "#999999";
        }
    }
}
