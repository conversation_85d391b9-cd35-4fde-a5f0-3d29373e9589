/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api;

import com.xinfei.vipcore.facade.rr.dto.VipClassifyAndIdentityDTO;
import com.xinfei.vocprod.biz.api.rr.*;
import com.xinfei.vocprod.biz.common.TechplayConstants;
import com.xinfei.vocprod.biz.log.DigestLogAnnotated;
import com.xinfei.vocprod.biz.log.QueryDigestLog;
import com.xinfei.vocprod.biz.service.IndexService;
import com.xinfei.vocprod.biz.service.VipService;
import com.xinfei.vocprod.biz.template.QueryCallBack;
import com.xinfei.vocprod.biz.template.QueryTemplate;
import com.xinfei.vocprod.biz.util.ParamCheckUtil;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_REQUEST;
import static com.xinfei.vocprod.biz.util.ParamCheckUtil.USER_REQUEST_USER;

@RestController
@RequestMapping("/vip")
@Slf4j
public class VipFacadeImpl {

    @Autowired
    private VipService vipService;

    @Autowired
    private IndexService indexService;

    @PostMapping("/getOperation")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public VipInfoResponse getVipOperation(@RequestBody VipRequest request) {

        final VipInfoResponse result = new VipInfoResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                String vipType = vipService.vipType(request.getUserNo());
                RefundLog refundDto = vipService.getRefundInfo(request.getUserNo(), vipType);
                VipNewOperation vipNewOperation = vipService.getOperationInfo(request.getUserNo(), vipType);
//                Object vipQuestions = vipService.getNewVipQuestions();
                result.setSuc(Boolean.TRUE);
                result.setRefundLog(refundDto);
                result.setVipNewOperation(vipNewOperation);
//                result.setVipQuestions(vipQuestions);
                log.info("getOperation traceId:{},request:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), JsonUtil.toJson(result));
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }


    @PostMapping("/isVip")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public IsVipResponse isVip(@RequestBody VipRequest request) {
        final IsVipResponse result = new IsVipResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                VipClassifyAndIdentityDTO info = vipService.vipClassifyAndIdentity(request.getUserNo());
                log.info("isVip traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), info);
                result.setSuc(Boolean.TRUE);
                if (info == null) {
                    result.setIsVip(Boolean.FALSE);
                } else {
                    result.setIsVip(info.getIsVip());
                    result.setVipType(info.getVipType());
                }
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }

    @PostMapping("/config")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public ConfigResponse config(@RequestBody VipRequest request) {
        final ConfigResponse result = new ConfigResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                Map<String, Object> configs = indexService.config(request.getUserNo());
                log.info("config traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), configs);
                result.setSuc(Boolean.TRUE);
                result.setConfigs(configs);
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }

    @PostMapping("/faqConfig")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public ConfigResponse faqConfig(@RequestBody VipRequest request) {
        final ConfigResponse result = new ConfigResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                Map<String, Object> configs = indexService.faqConfig(request.getUserNo());
                log.info("faqConfig traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), configs);
                result.setSuc(Boolean.TRUE);
                result.setConfigs(configs);
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }


    @PostMapping("/refundRequest")
    @DigestLogAnnotated(TechplayConstants.TRADE_DIGEST_LOG)
    public VipRefundResponse refund(@RequestBody VipRequest request) {
        final VipRefundResponse result = new VipRefundResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                RefundApplyLastRes resp = vipService.refundCard(request.getUserNo());
                log.info("refundRequest traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), resp);
                result.setSuc(Boolean.TRUE);
                result.setOperateResult(resp);
                log.info("refundRequest result traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), result);

            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }


    @PostMapping("/cancelRenewal")
    @DigestLogAnnotated(TechplayConstants.TRADE_DIGEST_LOG)
    public VipOperateResponse renew(@RequestBody VipRequest request) {
        final VipOperateResponse result = new VipOperateResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                Boolean resp = vipService.cancelRenewal(request.getUserNo());
                log.info("cancelRenewal traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), resp);
                result.setSuc(Boolean.TRUE);
                result.setOperateResult(resp);
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }


    @PostMapping("/cancelNextDeduction")
    @DigestLogAnnotated(TechplayConstants.TRADE_DIGEST_LOG)
    public VipNewOperateResponse next(@RequestBody VipRequest request) {
        final VipNewOperateResponse result = new VipNewOperateResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                CancelNextDeductionRes resp = vipService.cancelNextDeduction(request.getUserNo());
                log.info("cancelNextDeduction traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), resp);
                result.setSuc(Boolean.TRUE);
                result.setOperateResult(resp);
                log.info("cancelNextDeduction result traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), result);

            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }

    @PostMapping("/getVipUDeskLink")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public HelpCenterGetUdeskResultDto getVipUDeskLink(@RequestBody HelpCenterGetUdeskLinkRequest request) {
        final HelpCenterGetUdeskResultDto result = new HelpCenterGetUdeskResultDto();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                HelpCenterGetUdeskResultDto resp = indexService.getUdeskLink(request);
                log.info("getVipUDeskLink,req:{},result:{}", JsonUtil.toJson(request), resp);
                result.setSuc(Boolean.TRUE);
                result.setUrl(resp.getUrl());
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }


}
