/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.util;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version $ IDCardUtil, v 0.1 2024-01-17 15:36 junjie.yan Exp $
 */

public class IDCardUtil {

    /**
     * 15位身份证号
     */
    private static final Integer FIFTEEN_ID_CARD = 15;
    /**
     * 18位身份证号
     */
    private static final Integer EIGHTEEN_ID_CARD = 18;

    /**
     * 根据身份证号获取年龄
     *
     * @param iDCard 身份证
     * @return 年龄
     */
    public static Integer getAge(String iDCard) {
        int age = 0;
        if (StringUtils.isEmpty(iDCard)) {
            return age;
        }

        LocalDate date = LocalDate.now();
        int currYear = date.getYear();
        int currMonth = date.getMonth().getValue();

        // 身份证上的年份(15位身份证为1980年前的)
        if (iDCard.length() == FIFTEEN_ID_CARD) {
            String uYear = "19" + iDCard.substring(6, 8);
            String uMonth = iDCard.substring(8, 10);

            // 当前月份大于用户出身的月份表示已过生日
            if (Integer.parseInt(uMonth) <= currMonth) {
                age = currYear - Integer.parseInt(uYear) + 1;
            } else {
                age = currYear - Integer.parseInt(uYear);
            }

        } else if (iDCard.length() == EIGHTEEN_ID_CARD) {
            String uYear = iDCard.substring(6).substring(0, 4);
            String uMonth = iDCard.substring(10).substring(0, 2);

            // 当前月份大于用户出身的月份表示已过生日
            if (Integer.parseInt(uMonth) <= currMonth) {
                age = currYear - Integer.parseInt(uYear) + 1;
            } else {
                age = currYear - Integer.parseInt(uYear);
            }
        }

        return age;
    }


    /**
     * @param iDCard 身份证
     * @return 生日
     */
    public static String getBirthday(String iDCard) {
        if (StringUtils.isEmpty(iDCard)) {
            return "";
        }
        String birthday;
        String year = "";
        String month = "";
        String day = "";

        // 身份证上的年份(15位身份证为1980年前的)
        if (iDCard.length() == FIFTEEN_ID_CARD) {
            year = "19" + iDCard.substring(6, 8);
            month = iDCard.substring(8, 10);
            day = iDCard.substring(10, 12);
        } else if (iDCard.length() == EIGHTEEN_ID_CARD) {
            year = iDCard.substring(6).substring(0, 4);
            month = iDCard.substring(10).substring(0, 2);
            day = iDCard.substring(12).substring(0, 2);
        }
        birthday = year + "年" + month + "月" + day + "日";
        return birthday;
    }

    /**
     * 根据身份证号获取性别
     *
     * @param idCard 身份证号
     * @see
     */
    public static int getGenderFromIdCard(String idCard) {
        // 返回值定义：1-男，2-女，0-无效
        if (idCard == null || idCard.trim().isEmpty()) {
            return 0;
        }
        try {
            if (idCard.length() == 18) {
                // 18位身份证：取第17位字符（下标16）
                int digit = Integer.parseInt(idCard.substring(16, 17));
                return (digit % 2 == 0) ? 2 : 1;
            } else if (idCard.length() == 15) {
                // 15位身份证：取第15位字符（下标14）
                int digit = Integer.parseInt(idCard.substring(14, 15));
                return (digit % 2 == 0) ? 2 : 1;
            } else {
                // 长度不符合要求
                return 0;
            }
        } catch (NumberFormatException e) {
            // 如果解析失败，返回0
            return 0;
        }
    }

}