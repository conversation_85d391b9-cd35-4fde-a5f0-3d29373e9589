/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ RefundApplyLastRes, v 0.1 2024-11-12 17:56 junjie.yan Exp $
 */
@Data
public class RefundApplyLastRes {

    @ApiModelProperty("申请结果")
    private Boolean success;
    @ApiModelProperty("失败返回失败原因")
    private String message;
    @ApiModelProperty("支付信息")
    private PayAccount payAccountDTO;
    @ApiModelProperty("退款信息")
    private Long refundAmount;

}