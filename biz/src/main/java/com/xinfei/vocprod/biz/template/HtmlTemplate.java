/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import java.util.Map;

/**
 * HTML模板接口
 * 定义HTML模板的基本行为
 *
 * <AUTHOR>
 * @version $ HtmlTemplate, v 0.1 2024-12-20 AI Exp $
 */
public interface HtmlTemplate {

    /**
     * 渲染HTML内容
     *
     * @param data 数据对象
     * @param config 配置参数
     * @return 渲染后的HTML字符串
     */
    String render(Object data, Map<String, Object> config);

    /**
     * 获取模板名称
     */
    String getTemplateName();

    /**
     * 获取支持的数据类型
     */
    Class<?> getSupportedDataType();
}
