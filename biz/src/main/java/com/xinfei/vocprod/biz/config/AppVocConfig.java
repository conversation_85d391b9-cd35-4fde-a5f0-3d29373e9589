/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ AppVocConfig, v 0.1 2024-11-01 14:38 junjie.yan Exp $
 */
@Getter
@Component
@RefreshScope
public class AppVocConfig {

    @Value("${index.config.app_udesk}")
    private String appUDesk;

    @Value("${index.config.header}")
    private String header;

    @Value("${index.config.abtest}")
    private String abtest;

    @Value("${index.config.navigate}")
    private String navigate;

    //猜你想问
    @Value("${index.config.questions.1}")
    private String questions1;

    //权益
    @Value("${index.config.questions.2}")
    private String questions2;

    //借款
    @Value("${index.config.questions.3}")
    private String questions3;

    //还款
    @Value("${index.config.questions.4}")
    private String questions4;

    //账户
    @Value("${index.config.questions.5}")
    private String questions5;

    //结清
    @Value("${index.config.questions.6}")
    private String questions6;

    //提额卡
    @Value("${index.config.questions.7}")
    private String questions7;

    //飞跃会员权益
    @Value("${index.config.questions.8}")
    private String questions8;

    //费用
    @Value("${index.config.questions.9}")
    private String questions9;

    //逾期
    @Value("${index.config.questions.10}")
    private String questions10;

    //其他
    @Value("${index.config.questions.11}")
    private String questions11;

    @Value("${index.config.bottom}")
    private String bottom;

    @Value("${index.config.vip.navigate}")
    private String vipNavigate;

    @Value("${index.config.vipNoEntrance.navigate}")
    private String vipNoEntranceNavigate;

    @Value("${index.config.notVip.navigate}")
    private String notVipNavigate;
    //飞享会员猜你想问
    @Value("${index.config.vip.questions}")
    private String vipQuestions;

//    @Value("${index.config.vip.bottom}")
//    private String vipBottom;

    @Value("${index.config.question_category}")
    private String questionCategory;

    //飞享会员操作问题
    @Value("${index.operation.vip.questions}")
    private String vipOperationQuestions;

    //飞跃会员操作问题
    @Value("${index.operation.fei.yue.vip.questions}")
    private String vipOperationFeiYueQuestions;

    @Value("${foundational.model.timeout}")
    private Long foundationalModelTimeout;

}