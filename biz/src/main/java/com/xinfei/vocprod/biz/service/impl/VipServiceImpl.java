/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.service.impl;

import apollo.com.google.gson.reflect.TypeToken;
import com.google.gson.Gson;
import com.xinfei.supervip.common.enums.RefundStatusEnum;
import com.xinfei.supervip.interfaces.model.admin.dto.*;
import com.xinfei.vipcore.facade.rr.dto.*;
import com.xinfei.vipcore.facade.rr.response.ShowRefundEntryDTO;
import com.xinfei.vocprod.biz.api.rr.*;
import com.xinfei.vocprod.biz.common.RandomBizKey;
import com.xinfei.vocprod.biz.config.AppVocConfig;
import com.xinfei.vocprod.biz.config.VocProdApolloConfig;
import com.xinfei.vocprod.biz.mapstruct.VipCardConverter;
import com.xinfei.vocprod.biz.model.VipTag;
import com.xinfei.vocprod.biz.service.VipService;
import com.xinfei.vocprod.itl.impl.RandomGeneratorClientImpl;
import com.xinfei.vocprod.itl.impl.VipFacadeClientImpl;
import com.xinfei.vocprod.util.LogUtil;
import com.xinfei.vocprod.util.enums.MemberTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version $ UDeskServiceImpl, v 0.1 2024/7/8 14:10 wancheng.qu Exp $
 */
@Service
@Slf4j
public class VipServiceImpl implements VipService {

    @Resource
    private VipFacadeClientImpl vipFacadeClient;

    @Resource
    private RandomGeneratorClientImpl randomGeneratorClient;

    @Resource
    private VocProdApolloConfig vocProdApolloConfig;

    private final Gson gson = new Gson();
    private final Type type = new TypeToken<Map<String, Object>>() {
    }.getType();

    @Resource
    private AppVocConfig appVocConfig;


    @Override
    public VipOperation getVipInfo(String userNo) {
        VipOperation vipOperation = new VipOperation();
        List<String> tagKeys = new ArrayList<>();
        tagKeys.add(VipTag.canSelfCancelWithhold);
        tagKeys.add(VipTag.canSelfRefund);
        tagKeys.add(VipTag.canSelfCloseRenew);
        tagKeys.add(VipTag.canCustomerRefund);

        ComputeTagResultInfoDTO resultInfoDTO = vipFacadeClient.computeTagResult(userNo, tagKeys);
        if (resultInfoDTO != null && CollectionUtils.isNotEmpty(resultInfoDTO.getTagComputeResultList())) {
            for (TagComputeResultDTO tagComputeResultDTO : resultInfoDTO.getTagComputeResultList()) {
                switch (tagComputeResultDTO.getTagKey()) {
                    case VipTag.canSelfCancelWithhold:
                        vipOperation.setCanSelfCancelWithhold(tagComputeResultDTO.getTagResult());
                        break;
                    case VipTag.canSelfCloseRenew:
                        vipOperation.setCanSelfCloseRenew(tagComputeResultDTO.getTagResult());
                        break;
                    case VipTag.canSelfRefund:
                        vipOperation.setCanSelfRefund(tagComputeResultDTO.getTagResult());
                        if (tagComputeResultDTO.getTagResult()) {
                            vipOperation.setCanRefund(true);
                        }
                        break;
                    case VipTag.canCustomerRefund:
                        if (tagComputeResultDTO.getTagResult()) {
                            vipOperation.setCanRefund(true);
                        }
                        break;
                }
            }
        }
        if (vipOperation.getCanRefund() == null) {
            vipOperation.setCanRefund(false);
        }
        return vipOperation;
    }

    @Override
    public RefundLog getRefundInfo(String userNo, String vipType) {
        try {
            if (MemberTypeEnum.FEI_XIANG.getCode().equals(vipType)) {
                RefundListDto refundListDto = vipFacadeClient.vipCardRefundList(Long.valueOf(userNo));
                if (refundListDto != null && CollectionUtils.isNotEmpty(refundListDto.getList())) {
                    //退款状态 0:初始化退款中，1:退款成功，2:退款失败
                    RefundDto refundDto = null;
                    if (refundListDto.getList().stream().anyMatch(r -> r.getStatus() == 0)) {
                        refundDto = refundListDto.getList().stream().sorted(Comparator.comparing(RefundDto::getUpdateTime).reversed()).filter(r -> r.getStatus() == 0).collect(Collectors.toList()).get(0);
                    } else if (refundListDto.getList().stream().anyMatch(r -> r.getStatus() == 2)) {
                        refundDto = refundListDto.getList().stream().sorted(Comparator.comparing(RefundDto::getUpdateTime).reversed()).filter(r -> r.getStatus() == 2).collect(Collectors.toList()).get(0);
                    } else if (refundListDto.getList().stream().anyMatch(r -> r.getStatus() == 1)) {
                        refundDto = refundListDto.getList().stream().sorted(Comparator.comparing(RefundDto::getUpdateTime).reversed()).filter(r -> r.getStatus() == 1).collect(Collectors.toList()).get(0);
                    }
                    if (refundDto != null) {
                        PayAccountDTO payAccountDTO = vipFacadeClient.orderPayAccount(refundDto.getVipCardId().toString(), 3);
                        if (payAccountDTO == null) {
                            return null;
                        }

                        RefundLog refundLog = VipCardConverter.INSTANCE.refundDtoToRefundLog(refundDto);
                        refundLog.setRefundAccountType(payAccountDTO.getRefundAccountType());
                        if (refundLog.getRefundAccountType() == 1 && payAccountDTO.getPayOrderBankDTO() != null) {
                            refundLog.setRefundAccount(payAccountDTO.getPayOrderBankDTO().getTailBankNo());
                            refundLog.setRefundBankName(payAccountDTO.getPayOrderBankDTO().getBankName());
                        }
                        if (refundLog.getRefundAccountType() == 2) {
                            refundLog.setRefundAccount(payAccountDTO.getAliAccount());
                        }
                        return refundLog;
                    }
                }
            } else if (MemberTypeEnum.FEI_YUE.getCode().equals(vipType)) {
                RefundPageAdminDTO refundListDto = vipFacadeClient.queryUserRefundList(userNo, MemberTypeEnum.FEI_YUE.getCode());
                if (refundListDto != null && CollectionUtils.isNotEmpty(refundListDto.getList())) {
                    //退款状态 0:初始化退款中，1:退款成功，2:退款失败
                    RefundDetailAdminDTO refundDetailAdminDTO = null;
                    if (refundListDto.getList().stream().anyMatch(r -> RefundStatusEnum.REFUND_START.getCode().equals(r.getRefundStatus()) || RefundStatusEnum.REFUNDING.getCode().equals(r.getRefundStatus()))) {
                        refundDetailAdminDTO = refundListDto.getList().stream().sorted(Comparator.comparing(RefundDetailAdminDTO::getUpdateTime).reversed()).filter(r -> RefundStatusEnum.REFUND_START.getCode().equals(r.getRefundStatus()) || RefundStatusEnum.REFUNDING.getCode().equals(r.getRefundStatus())).collect(Collectors.toList()).get(0);
                    } else if (refundListDto.getList().stream().anyMatch(r -> RefundStatusEnum.REFUND_FAIL.getCode().equals(r.getRefundStatus()))) {
                        refundDetailAdminDTO = refundListDto.getList().stream().sorted(Comparator.comparing(RefundDetailAdminDTO::getUpdateTime).reversed()).filter(r -> RefundStatusEnum.REFUND_FAIL.getCode().equals(r.getRefundStatus())).collect(Collectors.toList()).get(0);
                    } else if (refundListDto.getList().stream().anyMatch(r -> RefundStatusEnum.REFUND_SUCCESS.getCode().equals(r.getRefundStatus()))) {
                        refundDetailAdminDTO = refundListDto.getList().stream().sorted(Comparator.comparing(RefundDetailAdminDTO::getUpdateTime).reversed()).filter(r -> RefundStatusEnum.REFUND_SUCCESS.getCode().equals(r.getRefundStatus())).collect(Collectors.toList()).get(0);
                    }
                    if (refundDetailAdminDTO != null) {
                        return VipCardConverter.INSTANCE.refundDetailAdminDtoToRefundLog(refundDetailAdminDTO);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取会员退款信息异常", e);
        }

        return null;
    }

    @Override
    public VipNewOperation getOperationInfo(String userNo, String vipType) {
        List<String> sceneKeys = new ArrayList<>();
        VipNewOperation vipNewOperation = new VipNewOperation();
        if (MemberTypeEnum.FEI_XIANG.getCode().equals(vipType)) {
            sceneKeys.add(VipTag.kfCanSelfRefund);
            sceneKeys.add(VipTag.kfCanSelfCloseRenew);
            sceneKeys.add(VipTag.kfCanSelfCancelWithhold);
            sceneKeys.add(VipTag.kfSelfRefundRetain);
        } else if (MemberTypeEnum.FEI_YUE.getCode().equals(vipType)) {
            sceneKeys.add(VipTag.FYVipKfCanSelfRefund);
            sceneKeys.add(VipTag.FYVipKfCanSelfCancelWithhold);
        } else {
            return vipNewOperation;
        }

        StrategyDecisionResultListDTO resultInfoDTO = vipFacadeClient.batchDecisionStrategy(userNo, sceneKeys);

        if (resultInfoDTO != null && CollectionUtils.isNotEmpty(resultInfoDTO.getDecisionResultList())) {
            for (StrategyDecisionResultDTO strategyDecisionResultDTO : resultInfoDTO.getDecisionResultList()) {
                switch (strategyDecisionResultDTO.getSceneKey()) {
                    case VipTag.kfCanSelfCancelWithhold:
                        vipNewOperation.setKfCanSelfCancelWithhold(strategyDecisionResultDTO.getMatchResult());
                        break;
                    case VipTag.FYVipKfCanSelfCancelWithhold:
                        vipNewOperation.setKfCanSelfCancelWithhold(strategyDecisionResultDTO.getMatchResult());
                        break;
                    case VipTag.kfCanSelfCloseRenew:
                        vipNewOperation.setKfCanSelfCloseRenew(strategyDecisionResultDTO.getMatchResult());
                        break;
                    case VipTag.kfCanSelfRefund:
                        vipNewOperation.setKfCanSelfRefund(strategyDecisionResultDTO.getMatchResult());
                        break;
                    case VipTag.FYVipKfCanSelfRefund:
                        vipNewOperation.setKfCanSelfRefund(strategyDecisionResultDTO.getMatchResult());
                        break;
                    case VipTag.kfSelfRefundRetain:
                        vipNewOperation.setKfSelfRefundRetain(strategyDecisionResultDTO.getMatchResult());
                        break;
                    default:
                        break;
                }
            }
        }

        return vipNewOperation;
    }

    @Override
    public Object getNewVipQuestions() {
        return gson.fromJson(appVocConfig.getVipOperationQuestions(), type);
    }

    @Override
    public Boolean isVip(String userNo) {

        if ("groupA".equals(randomGeneratorClient.ab(userNo, RandomBizKey.KFDT_HYJZX))) {
            return Boolean.FALSE;
        }

        List<String> tagKeys = new ArrayList<>();
        tagKeys.add(VipTag.validVipIdentity);

        try {
            ComputeTagResultInfoDTO resultInfoDTO = vipFacadeClient.computeTagResult(userNo, tagKeys);
            if (resultInfoDTO != null && CollectionUtils.isNotEmpty(resultInfoDTO.getTagComputeResultList())) {
                return resultInfoDTO.getTagComputeResultList().get(0).getTagResult();
            }
        } catch (Exception e) {
            log.error(LogUtil.infoLog("computeTagResult", "判断是否飞享会员失败", e));
        }

        return Boolean.FALSE;
    }

    @Override
    //  是否是会员（飞享/飞跃）
    public Boolean isVipNew(String userNo) {

        if ("groupA".equals(randomGeneratorClient.ab(userNo, RandomBizKey.KFDT_HYJZX))) {
            return Boolean.FALSE;
        }

        try {
            VipClassifyAndIdentityDTO resultInfoDTO = vipFacadeClient.vipClassifyAndIdentity(userNo);
            if (resultInfoDTO != null && resultInfoDTO.getIsVip() != null) {
                return resultInfoDTO.getIsVip();
            }
        } catch (Exception e) {
            log.error(LogUtil.infoLog("vipClassifyAndIdentity", "判断是否会员失败", e));
        }

        return Boolean.FALSE;
    }

    @Override
    public String vipType(String userNo) {
        try {
            VipClassifyAndIdentityDTO resultInfoDTO = vipFacadeClient.vipClassifyAndIdentity(userNo);
            if (resultInfoDTO != null) {
                return resultInfoDTO.getVipType();
            }
        } catch (Exception e) {
            log.error(LogUtil.infoLog("vipClassifyAndIdentity", "会员卡分流失败", e));
        }

        return "";
    }

    @Override
    public VipClassifyAndIdentityDTO vipClassifyAndIdentity(String userNo) {
        try {
            return vipFacadeClient.vipClassifyAndIdentity(userNo);
        } catch (Exception e) {
            log.error(LogUtil.infoLog("vipClassifyAndIdentity", "判断会员卡有效身份失败", e));
        }

        VipClassifyAndIdentityDTO vipClassifyAndIdentityDTO = new VipClassifyAndIdentityDTO();
        vipClassifyAndIdentityDTO.setIsVip(Boolean.FALSE);
        vipClassifyAndIdentityDTO.setVipType("");
        return vipClassifyAndIdentityDTO;
    }

    @Override
    public Boolean isSuperVip(String usrNo) {
        if (!vocProdApolloConfig.isSuperVipSwitch()) {
            return Boolean.FALSE;
        }
        try {
            VipUserStatusAdminDTO vipUserStatusDTO = vipFacadeClient.userSuperVipStatus(Long.valueOf(usrNo));
            return vipUserStatusDTO.getWhetherVip();
        } catch (Exception e) {
            // 暂时不打日志，下游已经打日志，兜底一律认为不是飞享会员
            return Boolean.FALSE;
        }
    }

    @Override
    public String vipClassifyGroup(String usrNo) {
        if (!vocProdApolloConfig.isSuperVipSwitch()) {
            return MemberTypeEnum.NON_MEMBER.getCode();
        }
        try {
            VipClassifyTypeDTO vipClassifyTypeDTO = vipFacadeClient.vipClassifyGroup(Long.valueOf(usrNo));
            if (Objects.isNull(vipClassifyTypeDTO) || StringUtils.isBlank(vipClassifyTypeDTO.getVipType())) {
                return MemberTypeEnum.NON_MEMBER.getCode();
            }
            return vipClassifyTypeDTO.getVipType();
        } catch (Exception e) {
            return MemberTypeEnum.NON_MEMBER.getCode();
        }
    }

    @Override
    public String vipClassifyType(String usrNo) {
        if (!vocProdApolloConfig.isSuperVipSwitch()) {
            return MemberTypeEnum.NON_MEMBER.getCode();
        }
        try {
            VipClassifyTypeDTO vipClassifyGroupDTO = vipFacadeClient.vipClassifyType(Long.valueOf(usrNo));
            if (Objects.isNull(vipClassifyGroupDTO) || StringUtils.isBlank(vipClassifyGroupDTO.getVipType())) {
                return MemberTypeEnum.NON_MEMBER.getCode();
            }
            return vipClassifyGroupDTO.getVipType();
        } catch (Exception e) {
            return MemberTypeEnum.NON_MEMBER.getCode();
        }
    }

    public Boolean showVipEntrance(String userNo) {
        boolean matchResult = Boolean.FALSE;

        try {
            ShowVipAdminEntryDTO resultInfoDTO = vipFacadeClient.showVipAdminEntry(userNo);
            if (resultInfoDTO != null) {
                matchResult = resultInfoDTO.getShowEntry();
            }
        } catch (Exception e) {
            log.error(LogUtil.infoLog("showVipAdminEntry", "判断会员入口展示失败", e));
        }

        log.info("isVip showVipEntrance userNo:{},matchResult:{}", userNo, matchResult);
        return matchResult;
    }

    @Override
    public Boolean showRefundEntry(String userNo) {
        try {
            ShowRefundEntryDTO result = vipFacadeClient.showRefundEntry(Long.parseLong(userNo));
            if (result != null && result.getShowEntry() != null) {
                return result.getShowEntry();
            }
        } catch (Exception e) {
            log.error(LogUtil.infoLog("showRefundEntry", "判断是否提额卡失败", e));
        }
        return false;
    }

    @Override
    public RefundApplyLastRes refundCard(String userNo) {
        RefundApplyLastRes refundApplyLastRes = new RefundApplyLastRes();

        if (MemberTypeEnum.FEI_XIANG.getCode().equals(vipType(userNo))) {
            RefundApplyLastResDto resDto = vipFacadeClient.refundApplyLast(Long.parseLong(userNo));
            refundApplyLastRes = VipCardConverter.INSTANCE.resDtoDtoToRefundApplyLastRes(resDto);
            if (refundApplyLastRes != null && "已有未执行的申请记录".equals(refundApplyLastRes.getMessage())) {
                refundApplyLastRes.setMessage("您有退款处理中订单，请5分钟后重新尝试");
            }
        } else if (MemberTypeEnum.FEI_YUE.getCode().equals(vipType(userNo))) {
            UserSelfRefundHandleResultAdminDTO result = vipFacadeClient.handleUserSelfRefundImmediately(userNo, MemberTypeEnum.FEI_YUE.getCode());
            if (result != null) {
                refundApplyLastRes.setSuccess(result.getSuccess());
                refundApplyLastRes.setMessage(result.getMessage());
                if(result.getSuccess()){
                    refundApplyLastRes.setRefundAmount(result.getRefundAmount().longValue());
                    PayOrderBankAdminDTO payOrderBankAdminDTO = result.getPayOrderBankAdminDTO();
                    if (payOrderBankAdminDTO != null) {
                        PayAccount payAccount = new PayAccount();
                        PayOrderBank payOrderBank = new PayOrderBank();
                        payOrderBank.setBankId(payOrderBankAdminDTO.getBankId());
                        payOrderBank.setBankName(payOrderBankAdminDTO.getBankName());
                        payOrderBank.setPayOrderNo(payOrderBankAdminDTO.getPayOrderNo());
                        payOrderBank.setTailBankNo(payOrderBankAdminDTO.getTailBankNo());
                        payAccount.setPayOrderBankDTO(payOrderBank);
                        refundApplyLastRes.setPayAccountDTO(payAccount);
                    }
                    if ("已有未执行的申请记录".equals(result.getMessage())) {
                        refundApplyLastRes.setMessage("您有退款处理中订单，请5分钟后重新尝试");
                    }
                }
            }
        } else {
            refundApplyLastRes.setSuccess(Boolean.FALSE);
            refundApplyLastRes.setMessage("当前无有效订单，请重新尝试");
        }

        return refundApplyLastRes;
    }

    public Boolean cancelRenewal(String userNo) {

        Boolean result = vipFacadeClient.renewStop(Long.parseLong(userNo));
        if (result != null) {
            return result;
        }

        return false;
    }

    public CancelNextDeductionRes cancelNextDeduction(String userNo) {
        CancelNextDeductionRes cancelNextDeductionRes = new CancelNextDeductionRes();

        if (MemberTypeEnum.FEI_XIANG.getCode().equals(vipType(userNo))) {
            Boolean result = vipFacadeClient.stopWithholdLast(Long.parseLong(userNo));
            if (result != null) {
                cancelNextDeductionRes.setSuccess(result);
                return cancelNextDeductionRes;
            }
        } else if (MemberTypeEnum.FEI_YUE.getCode().equals(vipType(userNo))) {
            VipOpsResultAdminDTO result = vipFacadeClient.handleUserSelfCancelVipDeduct(userNo, MemberTypeEnum.FEI_YUE.getCode());
            if (result != null) {
                cancelNextDeductionRes.setSuccess(result.getSuccess());
                cancelNextDeductionRes.setMessage(result.getMessage());
                return cancelNextDeductionRes;
            }
        } else {
            cancelNextDeductionRes.setSuccess(Boolean.FALSE);
            cancelNextDeductionRes.setMessage("当前无有效订单，请重新尝试");
        }

        return cancelNextDeductionRes;
    }
}