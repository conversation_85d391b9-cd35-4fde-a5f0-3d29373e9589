/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * HTML模板工厂类
 * 负责创建和管理各种HTML模板实例
 *
 * <AUTHOR>
 * @version $ HtmlTemplateFactory, v 0.1 2025/1/2 14:35 shaohui.chen Exp $
 */
@Component
public class HtmlTemplateFactory {

    /**
     * 模板实例缓存
     */
    private static final Map<TemplateType, HtmlTemplate> TEMPLATE_CACHE = new HashMap<>();

    static {
        // 初始化所有模板实例
        TEMPLATE_CACHE.put(TemplateType.BILL_TIMELINE, new BillTimelineTemplate());
        TEMPLATE_CACHE.put(TemplateType.MEMBERSHIP_CARD_LIST, new MembershipCardListTemplate());
        TEMPLATE_CACHE.put(TemplateType.ORDER_LIST, new OrderListTemplate());
        TEMPLATE_CACHE.put(TemplateType.PAYMENT_RECORD, new PaymentRecordTemplate());
        TEMPLATE_CACHE.put(TemplateType.LOAN_SUMMARY, new LoanSummaryTemplate());
        TEMPLATE_CACHE.put(TemplateType.REFUND_HISTORY, new RefundHistoryTemplate());
    }

    /**
     * 获取模板实例
     *
     * @param templateType 模板类型
     * @return 模板实例
     * @throws IllegalArgumentException 如果模板类型不支持
     */
    public HtmlTemplate getTemplate(TemplateType templateType) {
        HtmlTemplate template = TEMPLATE_CACHE.get(templateType);
        if (template == null) {
            throw new IllegalArgumentException("Unsupported template type: " + templateType);
        }
        return template;
    }

    /**
     * 注册新的模板实例
     * 用于动态添加新模板类型
     *
     * @param templateType 模板类型
     * @param template 模板实例
     */
    public void registerTemplate(TemplateType templateType, HtmlTemplate template) {
        TEMPLATE_CACHE.put(templateType, template);
    }

    /**
     * 获取所有支持的模板类型
     *
     * @return 支持的模板类型数组
     */
    public TemplateType[] getSupportedTemplateTypes() {
        return TEMPLATE_CACHE.keySet().toArray(new TemplateType[0]);
    }

    /**
     * 检查是否支持指定的模板类型
     *
     * @param templateType 模板类型
     * @return 是否支持
     */
    public boolean isSupported(TemplateType templateType) {
        return TEMPLATE_CACHE.containsKey(templateType);
    }
}
