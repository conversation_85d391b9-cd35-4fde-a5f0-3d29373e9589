/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * HTML模板工厂类
 * 支持Spring Bean注入和自动发现模板
 *
 * <AUTHOR>
 * @version $ HtmlTemplateFactory, v 0.1 2025/1/2 14:35 shaohui.chen Exp $
 */
@Component
public class HtmlTemplateFactory {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 模板实例缓存
     */
    private final Map<TemplateType, HtmlTemplate> templateCache = new HashMap<>();

    /**
     * 初始化方法，自动发现并注册所有模板Bean
     */
    @PostConstruct
    public void init() {
        // 自动发现所有HtmlTemplate类型的Bean
        Map<String, HtmlTemplate> templateBeans = applicationContext.getBeansOfType(HtmlTemplate.class);
        
        for (HtmlTemplate template : templateBeans.values()) {
            TemplateType templateType = getTemplateTypeByClass(template.getClass());
            if (Objects.nonNull(templateType)) {
                templateCache.put(templateType, template);
            }
        }
        
        // 如果某些模板没有通过Spring注册，使用默认实例
        ensureAllTemplatesRegistered();
    }

    /**
     * 获取模板实例
     *
     * @param templateType 模板类型
     * @return 模板实例
     * @throws IllegalArgumentException 如果模板类型不支持
     */
    public HtmlTemplate getTemplate(TemplateType templateType) {
        HtmlTemplate template = templateCache.get(templateType);
        if (Objects.isNull(template)) {
            throw new IllegalArgumentException("Unsupported template type: " + templateType);
        }
        return template;
    }

    /**
     * 根据模板类获取对应的模板类型
     */
    private TemplateType getTemplateTypeByClass(Class<?> templateClass) {
        if (templateClass == BillTimelineTemplate.class) {
            return TemplateType.BILL_TIMELINE;
        } else if (templateClass == MembershipCardListTemplate.class) {
            return TemplateType.MEMBERSHIP_CARD_LIST;
        } else if (templateClass == OrderListTemplate.class) {
            return TemplateType.ORDER_LIST;
        } else if (templateClass == PaymentRecordTemplate.class) {
            return TemplateType.PAYMENT_RECORD;
        } else if (templateClass == LoanSummaryTemplate.class) {
            return TemplateType.LOAN_SUMMARY;
        } else if (templateClass == RefundHistoryTemplate.class) {
            return TemplateType.REFUND_HISTORY;
        }
        return null;
    }

    /**
     * 确保所有模板类型都已注册
     */
    private void ensureAllTemplatesRegistered() {
        for (TemplateType templateType : TemplateType.values()) {
            if (!templateCache.containsKey(templateType)) {
                HtmlTemplate template = createDefaultTemplate(templateType);
                if (template != null) {
                    templateCache.put(templateType, template);
                }
            }
        }
    }

    /**
     * 创建默认模板实例
     */
    private HtmlTemplate createDefaultTemplate(TemplateType templateType) {
        switch (templateType) {
            case BILL_TIMELINE:
                return new BillTimelineTemplate();
            case MEMBERSHIP_CARD_LIST:
                return new MembershipCardListTemplate();
            case ORDER_LIST:
                return new OrderListTemplate();
            case PAYMENT_RECORD:
                return new PaymentRecordTemplate();
            case LOAN_SUMMARY:
                return new LoanSummaryTemplate();
            case REFUND_HISTORY:
                return new RefundHistoryTemplate();
            default:
                return null;
        }
    }

    /**
     * 检查是否支持指定的模板类型
     */
    public boolean isSupported(TemplateType templateType) {
        return templateCache.containsKey(templateType);
    }

    /**
     * 获取所有支持的模板类型
     */
    public TemplateType[] getSupportedTemplateTypes() {
        return templateCache.keySet().toArray(new TemplateType[0]);
    }
}
