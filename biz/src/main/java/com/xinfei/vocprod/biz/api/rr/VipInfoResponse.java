/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api.rr;

import com.xinfei.xfframework.common.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ VipInfoResponse, v 0.1 2024-10-29 15:25 junjie.yan Exp $
 */
@Data
public class VipInfoResponse extends BaseResponse {

    @ApiModelProperty(value = "退款记录")
    private RefundLog refundLog;

    @ApiModelProperty(value = "新会员卡操作")
    private VipNewOperation vipNewOperation;

    @ApiModelProperty(value = "会员卡问题")
    private Object vipQuestions;

}