/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 支付记录HTML模板
 *
 * <AUTHOR>
 * @version $ PaymentRecordTemplate, v 0.1 2025/1/2 14:30 shaohui.chen Exp $
 */
@Component
public class PaymentRecordTemplate implements HtmlTemplate {

    @Override
    public String render(Object data, Map<String, Object> config) {
        // TODO: 实现支付记录模板
        return "<div>支付记录模板待实现</div>";
    }

    @Override
    public String getTemplateName() {
        return "支付记录";
    }

    @Override
    public Class<?> getSupportedDataType() {
        return Object.class;
    }
}
