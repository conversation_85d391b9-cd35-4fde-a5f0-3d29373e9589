/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HTML模板管理器
 * 用于管理各种自定义卡片的HTML模板
 *
 * <AUTHOR>
 * @version $ HtmlTemplateManager, v 0.1 2025/1/2 14:30 shaohui.chen Exp $
 */
@Component
public class HtmlTemplateManager {

    /**
     * 构建HTML内容
     *
     * @param templateType 模板类型
     * @param data 数据
     * @param config 配置参数
     * @return HTML字符串
     */
    public String buildHtml(TemplateType templateType, Object data, Map<String, Object> config) {
        HtmlTemplate template = getTemplate(templateType);
        return template.render(data, config);
    }

    /**
     * 获取模板实例
     */
    private HtmlTemplate getTemplate(TemplateType templateType) {
        switch (templateType) {
            case BILL_TIMELINE:
                return new BillTimelineTemplate();
            case MEMBERSHIP_CARD_LIST:
                return new MembershipCardListTemplate();
            case ORDER_LIST:
                return new OrderListTemplate();
            case PAYMENT_RECORD:
                return new PaymentRecordTemplate();
            default:
                throw new IllegalArgumentException("Unsupported template type: " + templateType);
        }
    }

}
