/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HTML模板管理器
 * 用于管理各种自定义卡片的HTML模板
 *
 * <AUTHOR>
 * @version $ HtmlTemplateManager, v 0.1 2024-12-20 AI Exp $
 */
@Component
public class HtmlTemplateManager {

    /**
     * 构建HTML内容
     *
     * @param templateType 模板类型
     * @param data 数据
     * @param config 配置参数
     * @return HTML字符串
     */
    public String buildHtml(TemplateType templateType, Object data, Map<String, Object> config) {
        HtmlTemplate template = getTemplate(templateType);
        return template.render(data, config);
    }

    /**
     * 获取模板实例
     */
    private HtmlTemplate getTemplate(TemplateType templateType) {
        switch (templateType) {
            case BILL_TIMELINE:
                return new BillTimelineTemplate();
            case MEMBERSHIP_CARD_LIST:
                return new MembershipCardListTemplate();
            case ORDER_LIST:
                return new OrderListTemplate();
            case PAYMENT_RECORD:
                return new PaymentRecordTemplate();
            default:
                throw new IllegalArgumentException("Unsupported template type: " + templateType);
        }
    }

    /**
     * 模板类型枚举
     */
    public enum TemplateType {
        BILL_TIMELINE("账单时间轴"),
        MEMBERSHIP_CARD_LIST("会员卡列表"),
        ORDER_LIST("订单列表"),
        PAYMENT_RECORD("支付记录"),
        LOAN_SUMMARY("借款汇总"),
        REFUND_HISTORY("退款历史");

        private final String description;

        TemplateType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
