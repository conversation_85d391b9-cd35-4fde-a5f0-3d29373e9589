/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * HTML模板管理器
 * 用于管理各种自定义卡片的HTML模板
 *
 * <AUTHOR>
 * @version $ HtmlTemplateManager, v 0.1 2025/1/2 14:30 shaohui.chen Exp $
 */
@Component
public class HtmlTemplateManager {

    @Autowired
    private HtmlTemplateFactory templateFactory;

    /**
     * 构建HTML内容
     *
     * @param templateType 模板类型
     * @param data 数据
     * @param config 配置参数
     * @return HTML字符串
     */
    public String buildHtml(TemplateType templateType, Object data, Map<String, Object> config) {
        HtmlTemplate template = templateFactory.getTemplate(templateType);
        return template.render(data, config);
    }

    /**
     * 检查是否支持指定的模板类型
     *
     * @param templateType 模板类型
     * @return 是否支持
     */
    public boolean isSupported(TemplateType templateType) {
        return templateFactory.isSupported(templateType);
    }

    /**
     * 获取所有支持的模板类型
     *
     * @return 支持的模板类型数组
     */
    public TemplateType[] getSupportedTemplateTypes() {
        return templateFactory.getSupportedTemplateTypes();
    }

}
