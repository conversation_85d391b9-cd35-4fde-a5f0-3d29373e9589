package com.xinfei.vocprod.biz.service;

import com.xinfei.vipcore.facade.rr.dto.VipClassifyAndIdentityDTO;
import com.xinfei.vocprod.biz.api.rr.*;

import java.util.Map;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
public interface VipService {
    VipOperation getVipInfo(String userNo);

    RefundLog getRefundInfo(String userNo, String vipType);

    VipNewOperation getOperationInfo(String userNo, String vipType);

    Object getNewVipQuestions();

    Boolean isVip(String userNo);

    Boolean isVipNew(String userNo);

    String vipType(String userNo);

    VipClassifyAndIdentityDTO vipClassifyAndIdentity(String userNo);

    Boolean isSuperVip(String usrNo);

    String vipClassifyGroup(String usrNo);

    String vipClassifyType(String usrNo);

    Boolean showVipEntrance(String userNo);

    Boolean showRefundEntry(String userNo);

    RefundApplyLastRes refundCard(String userNo);

    Boolean cancelRenewal(String userNo);

    CancelNextDeductionRes cancelNextDeduction(String userNo);
}
