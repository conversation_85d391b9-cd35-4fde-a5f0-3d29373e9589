/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import com.xinfei.vocprod.facade.response.CustomCardContent;
import com.xinfei.vocprod.facade.response.VocKeyResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义卡片构建器
 * 统一管理各种自定义卡片的构建逻辑
 *
 * <AUTHOR>
 * @version $ CustomCardBuilder, v 0.1 2024-12-20 AI Exp $
 */
@Component
public class CustomCardBuilder {

    @Autowired
    private HtmlTemplateManager htmlTemplateManager;

    /**
     * 构建自定义卡片消息
     *
     * @param config 卡片配置
     * @return VocKeyResponse.Message
     */
    public VocKeyResponse.Message buildCustomCardMessage(CardConfig config) {
        VocKeyResponse.Message message = new VocKeyResponse.Message();
        message.setMsgType("custom_card");

        CustomCardContent cardContent = buildCustomCardContent(config);
        message.setMsgContent(cardContent);

        return message;
    }

    /**
     * 构建自定义卡片内容
     */
    private CustomCardContent buildCustomCardContent(CardConfig config) {
        CustomCardContent content = new CustomCardContent();
        content.setId(config.getId());
        content.setName(config.getName());
        content.setTurnFlag(config.getTurnFlag());
        content.setShowSize(config.getShowSize());
        
        // 处理标题和时间戳
        if (config.isShowTimestamp()) {
            LocalDateTime now = LocalDateTime.now();
            String timeStamp = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            content.setTitle(config.getSpeakerName() + "    " + timeStamp);
        } else {
            content.setTitle(config.getTitle());
        }
        content.setSubTitle(config.getSubTitle());

        // 构建卡片列表
        List<CustomCardContent.CardItem> cardList = new ArrayList<>();
        
        if (config.isUseTemplate()) {
            // 使用模板构建
            CustomCardContent.CardItem cardItem = new CustomCardContent.CardItem();
            cardItem.setId(config.getCardId());
            cardItem.setType(1); // 富文本卡片
            cardItem.setIsHit(config.isClickable() ? 1 : 0);
            
            if (config.isClickable()) {
                cardItem.setHitType(config.getHitType());
                cardItem.setHitContent(config.getHitContent());
                cardItem.setHitShowContent(config.getHitShowContent());
            }

            // 使用模板生成HTML内容
            String htmlContent = htmlTemplateManager.buildHtml(
                config.getTemplateType(), 
                config.getData(), 
                config.getTemplateConfig()
            );
            cardItem.setContent(htmlContent);
            
            cardList.add(cardItem);
        } else {
            // 自定义构建逻辑
            cardList = config.getCustomCardItems();
        }

        content.setCardList(cardList);
        return content;
    }

    /**
     * 卡片配置类
     */
    public static class CardConfig {
        private Integer id;
        private String name;
        private Integer turnFlag = 0;
        private Integer showSize;
        private String title;
        private String subTitle;
        private boolean showTimestamp = false;
        private String speakerName = "小飞";
        private String cardId;
        private boolean clickable = false;
        private Integer hitType;
        private String hitContent;
        private String hitShowContent;
        private boolean useTemplate = true;
        private HtmlTemplateManager.TemplateType templateType;
        private Object data;
        private Map<String, Object> templateConfig = new HashMap<>();
        private List<CustomCardContent.CardItem> customCardItems = new ArrayList<>();

        // Getters and Setters
        public Integer getId() { return id; }
        public void setId(Integer id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public Integer getTurnFlag() { return turnFlag; }
        public void setTurnFlag(Integer turnFlag) { this.turnFlag = turnFlag; }
        
        public Integer getShowSize() { return showSize; }
        public void setShowSize(Integer showSize) { this.showSize = showSize; }
        
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public String getSubTitle() { return subTitle; }
        public void setSubTitle(String subTitle) { this.subTitle = subTitle; }
        
        public boolean isShowTimestamp() { return showTimestamp; }
        public void setShowTimestamp(boolean showTimestamp) { this.showTimestamp = showTimestamp; }
        
        public String getSpeakerName() { return speakerName; }
        public void setSpeakerName(String speakerName) { this.speakerName = speakerName; }
        
        public String getCardId() { return cardId; }
        public void setCardId(String cardId) { this.cardId = cardId; }
        
        public boolean isClickable() { return clickable; }
        public void setClickable(boolean clickable) { this.clickable = clickable; }
        
        public Integer getHitType() { return hitType; }
        public void setHitType(Integer hitType) { this.hitType = hitType; }
        
        public String getHitContent() { return hitContent; }
        public void setHitContent(String hitContent) { this.hitContent = hitContent; }
        
        public String getHitShowContent() { return hitShowContent; }
        public void setHitShowContent(String hitShowContent) { this.hitShowContent = hitShowContent; }
        
        public boolean isUseTemplate() { return useTemplate; }
        public void setUseTemplate(boolean useTemplate) { this.useTemplate = useTemplate; }
        
        public HtmlTemplateManager.TemplateType getTemplateType() { return templateType; }
        public void setTemplateType(HtmlTemplateManager.TemplateType templateType) { this.templateType = templateType; }
        
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        
        public Map<String, Object> getTemplateConfig() { return templateConfig; }
        public void setTemplateConfig(Map<String, Object> templateConfig) { this.templateConfig = templateConfig; }
        
        public List<CustomCardContent.CardItem> getCustomCardItems() { return customCardItems; }
        public void setCustomCardItems(List<CustomCardContent.CardItem> customCardItems) { this.customCardItems = customCardItems; }
    }
}
