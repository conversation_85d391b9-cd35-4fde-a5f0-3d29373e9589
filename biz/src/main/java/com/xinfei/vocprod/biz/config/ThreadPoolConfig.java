/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @version $ ThreadPoolConfig, v 0.1 2025-04-14 11:47 junjie.yan Exp $
 */
@Configuration
@Slf4j
public class ThreadPoolConfig {

    @Bean("asyncExecutor")
    public Executor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数（常驻线程数）
        executor.setCorePoolSize(10);
        // 最大线程数
        executor.setMaxPoolSize(20);
        // 队列容量（超过此值的请求会触发拒绝策略）
        executor.setQueueCapacity(500);
        // 线程名称前缀
        executor.setThreadNamePrefix("async-task-");
        // 拒绝策略（默认AbortPolicy）
        executor.setRejectedExecutionHandler(new java.util.concurrent.RejectedExecutionHandler() {
            @Override
            public void rejectedExecution(Runnable r, java.util.concurrent.ThreadPoolExecutor e) {
                log.error("任务被拒绝: " + r.toString() + " 线程池状态: " + e);
            }
        });
        // 初始化线程池
        executor.initialize();
        return executor;
    }

}