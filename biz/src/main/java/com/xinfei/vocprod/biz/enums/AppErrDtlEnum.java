/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.enums;

import lombok.Getter;

/**
 * 错误码明显段定义枚举
 *
 * <p>本枚举的code对应于标准错误码10~12位。
 * 而errorLevel对应于标准错误码的第4位
 *
 * <p>在标准错误码的位置如下：
 *     <table border="1">
 *     <tr>
 *     <td>位置</td><td>1</td><td>2</td><td>3</td><td bgcolor="yellow">4</td><td>5</td><td>6</td><td>7</td><td>8</td><td>9</td><td bgcolor="red">10</td><td bgcolor="red">11</td><td bgcolor="red">12</td>
 *     </tr>
 *     <tr>
 *     <td>示例</td><td>X</td><td>E</td><td>0</td><td>1</td><td>0</td><td>1</td><td>0</td><td>1</td><td>1</td><td>0</td><td>2</td><td>7</td>
 *     </tr>
 *     <tr>
 *     <td>说明</td><td colspan=2>固定<br>标识</td><td>规<br>范<br>版<br>本</td><td>错<br>误<br>级<br>别</td><td>错<br>误<br>类<br>型</td><td colspan=4>错误场景</td><td colspan=3>错误编<br>码</td>
 *     </tr>
 *     </table>
 *
 * <p>错误明细码的CODE取值空间如下：
 * <ul>
 *     <li>公共类错误码[000-099,999]
 *     <li>事务管理类错误码[100-149]
 *     <li>支用还款等交易类错误码[150-245]
 *     <li>日终处理类错误码[250-299]
 *     <li>查询类错误码[300-349]
 *     <li>管理类错误码[350-399]
 * </ul>
 *
 * <AUTHOR>
 * @version $ TechplayErrDtlEnum, v 0.1 2023/8/28 17:42 Jinyan.Huang Exp $
 */
@Getter
public enum AppErrDtlEnum {

    UNKNOWN_EXCEPTION("999", ErrorLevelsEnum.ERROR, "其它未知异常"),
    CONFIGURATION_ERROR("001", ErrorLevelsEnum.FATAL, "配置错误"),
    DB_EXCEPTION("002", ErrorLevelsEnum.ERROR, "数据库异常"),
    DATA_UPDATE_EXCEPTION("003", ErrorLevelsEnum.ERROR, "数据库异常"),
    PARAS_ERROR("004", ErrorLevelsEnum.ERROR, "参数错误"),
    CLIENT_SYS_ERROR("10003", ErrorLevelsEnum.ERROR, "当前人数较多,请稍后再试"),
    REQ_PARAM_NOT_VALID("101", ErrorLevelsEnum.WARN, "服务调用请求信息不合法"),

    // 全局异常
    AUTH_TOKEN_USER_ERROR("100001", ErrorLevelsEnum.ERROR, "token和user-no不能全部为空"),
    REQUEST_ID_IS_EMPTY("100002", ErrorLevelsEnum.ERROR, "request-id不能为空"),

    ACTIVITY_IS_CLOSE("110001", ErrorLevelsEnum.ERROR, "抱歉，您不符合领取条件或活动已关闭"),
    COUPON_IS_REWARDING("110002", ErrorLevelsEnum.ERROR, "优惠券发放中，请勿重复点击"),
    COUPON_IS_REWARDED("110003", ErrorLevelsEnum.ERROR, "抱歉，优惠券不可重复领取"),
    COUPON_SEND_FAILED("110004", ErrorLevelsEnum.ERROR, "优惠券发放失败，请联系在线客服"),

    // 业务异常
    NOT_POTENTIAL_USER("200001", ErrorLevelsEnum.ERROR, "不满足潜力用户条件"),
    GET_POTENTIAL_FAIL("200002", ErrorLevelsEnum.ERROR, "潜力额度获取失败"),
    LOAN_TYPE_GET_FAIL("200003", ErrorLevelsEnum.ERROR, "在贷类型获取失败"),
    PLEASE_LOGIN("200004", ErrorLevelsEnum.ERROR, "请先登录"),
    TAG_NOT_FOUND("200005", ErrorLevelsEnum.ERROR, "标签不存在"),
    TAG_DEFINE_ILLEGAL("200006", ErrorLevelsEnum.ERROR, "标签定义不合法"),
    REQUEST_USER_NOT_MATCH("200007", ErrorLevelsEnum.ERROR, "请求用户不匹配"),
    GET_OWNER_SHIP_FAIL("200008", ErrorLevelsEnum.ERROR, "状态归属读取失败"),
    COUPON_CONFIG_ERROR("200009", ErrorLevelsEnum.ERROR, "优惠券配置异常"),
    COUPON_CONFIG_ERROR2("200010", ErrorLevelsEnum.ERROR, "优惠券配置异常"),
    COUPON_CONFIG_ERROR3("200011", ErrorLevelsEnum.ERROR, "优惠券配置异常"),
    USER_NOT_EXIST("200012", ErrorLevelsEnum.ERROR, "用户不存在"),
    UDESK_GET_FIELD("200013", ErrorLevelsEnum.ERROR, "链接获取失败"),
    POTENTIAL_AMOUNT_INIT("200014", ErrorLevelsEnum.ERROR, "潜力额度生成中"),
    ORDER_STATUS_CHANGE("200015", ErrorLevelsEnum.ERROR, "订单已发生变更，请刷新APP"),
    VIP_RENEW_FAIL("200016", ErrorLevelsEnum.ERROR, "会员续费失败"),


    SMS_REQUEST_TOO_QUICK("200100", ErrorLevelsEnum.ERROR, "短信请求太频繁"),
    SMS_REQUEST_TOO_MANY("200101", ErrorLevelsEnum.ERROR, "短信请求太频繁"),
    SMS_VERIFY_FAIL("200102", ErrorLevelsEnum.ERROR, "验证码错误"),
    SMS_VERIFY_EXPIRE("200103", ErrorLevelsEnum.ERROR, "验证码已失效，请重新请求"),
    SMS_CONFIG_NOT_EXIST("200104", ErrorLevelsEnum.ERROR, "验证码发送异常，请联系客服"),
    SMS_MOBILE_FORMAT_ERROR("200105", ErrorLevelsEnum.ERROR, "验证码发送异常，请稍后重试"),
    SMS_SEND_ERROR("200106", ErrorLevelsEnum.ERROR, "验证码发送异常，请稍后重试"),
    SMS_CODE_NULL("200107", ErrorLevelsEnum.ERROR, "验证码不能为空"),
    SMS_MOBILE_FORMAT_ERROR2("200108", ErrorLevelsEnum.ERROR, "校验失败，请稍后重试"),
    SMS_VERIFY_FAIL2("200109", ErrorLevelsEnum.ERROR, "校验失败，请联系客服"),
    SMS_VERIFY_TOO_MANY("200110", ErrorLevelsEnum.ERROR, "请重新请求验证码"),
    SMS_MOBILE_FORMAT_ERROR3("200111", ErrorLevelsEnum.ERROR, "手机号有误，请重新输入"),
    SMS_BIZ_TYPE_ERROR("200112", ErrorLevelsEnum.ERROR, "参数有误"),
    MOBILE_EXIST("200113", ErrorLevelsEnum.ERROR, "新手机号已注册，请更换其他号码"),
    REAL_NAME_CHECK_FAIL("200114", ErrorLevelsEnum.ERROR, "该手机号实名信息不一致，请使用您本人的手机号。（新办理的手机号需要等待90天后才能同步实名信息）"),
    CHANGE_MOBILE_FAIL("200115", ErrorLevelsEnum.ERROR, "修改失败，请稍后再试"),
    MOBILE_CAN_NOT_SAME("200116", ErrorLevelsEnum.ERROR, "手机号不能和当前相同"),
    LOGOUT_FAIL("200117", ErrorLevelsEnum.ERROR, "修改失败，请稍后再试"),
    CHANGE_MOBILE_LIMIT("200118", ErrorLevelsEnum.ERROR, "修改次数过多，请明日再来"),

    // client异常
    CLIENT_STATUS_QUERY_ERROR("300001", ErrorLevelsEnum.ERROR, "额度查询失败，请稍后再试"),

    // 特殊异常，触发App交互
    LOGIN_EXPIRED("400001", ErrorLevelsEnum.ERROR, "您的登录状态已失效，请重新登录"),

    //提额专区
    INCREASE_UPLOADING("999999", ErrorLevelsEnum.ERROR, "操作频繁，请稍后再试"),
    INCREASE_FREQUENT("999998", ErrorLevelsEnum.ERROR, "操作频繁，请明天再试"),
    INCREASE_USER_ERROR("900000", ErrorLevelsEnum.ERROR, "获取用户信息失败"),
    INCREASE_HAS_PROCESSING_IMG("900001", ErrorLevelsEnum.ERROR, "存在处理中的图片，请勿重复上传"),
    INCREASE_COMPLETED("900002", ErrorLevelsEnum.ERROR, "已完成认证，请勿重复上传"),
    INCREASE_SYSTEM_BUSY("900003", ErrorLevelsEnum.ERROR, "系统繁忙，请稍后重试!"),
    INCREASE_IMAGE_UPLOAD_FAILED("900004", ErrorLevelsEnum.ERROR, "图片上传失败"),
    INCREASE_ORDER_RETRIEVAL_FAILED("900005", ErrorLevelsEnum.ERROR, "获取订单信息失败"),
    INCREASE_RECOGNITION_FAILED("900006", ErrorLevelsEnum.ERROR, "识别认证失败，请重新认证"),
    INCREASE_NO_STATUS_AUTHENTICATION_SUCCESS("900007", ErrorLevelsEnum.ERROR, "订单状态不是认证成功"),
    INCREASE_AMOUNT_STATUS_SUCCESS("900008", ErrorLevelsEnum.ERROR, "提额成功，请勿重复提交"),
    INCREASE_AMOUNT_STATUS_INIT("900009", ErrorLevelsEnum.ERROR, "提交成功，请勿重复提交"),
    FORENSIC_STARTUP_FAILED("900010", ErrorLevelsEnum.ERROR, "启动VDI取证失败"),
    CONTRACT_GENERATION_FAILED("900011", ErrorLevelsEnum.ERROR, "生成合同失败"),
    RECOGNITION_ERR_MSG("900012", ErrorLevelsEnum.ERROR, "识别失败，可按示例标准重新截图上传"),
    INCREASE_INDEX_APPLY("900013", ErrorLevelsEnum.ERROR, "请先回首页完成额度申请"),
    ;

    private final String code;

    private final ErrorLevelsEnum errorLevel;

    private final String description;

    /**
     * 私有构造函数。
     *
     * @param code        枚举编码
     * @param errorLevel  错误级别
     * @param description 描述说明
     */
    AppErrDtlEnum(String code, ErrorLevelsEnum errorLevel, String description) {
        this.code = code;
        this.errorLevel = errorLevel;
        this.description = description;
    }

    public static AppErrDtlEnum getByCode(String code) {
        for (AppErrDtlEnum detailCode : values()) {
            if (detailCode.getCode().equals(code)) {

                return detailCode;
            }
        }
        return null;
    }
}