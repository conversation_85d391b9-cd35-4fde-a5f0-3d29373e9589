/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.model;

/**
 * <AUTHOR>
 * @version $ VipTag, v 0.1 2024-10-29 15:03 junjie.yan Exp $
 */

/**
 * 会员卡服务标签
 */
public class VipTag {
    //标签
    //是否可取消下次扣款
    public static final String canSelfCancelWithhold = "can_self_cancel_withhold";
    //是否可自助退款
    public static final String canSelfRefund = "can_self_refund";

    //是否可取消续费
    public static final String canSelfCloseRenew = "can_self_close_renew";

    //是否是飞享会员
    public static final String validVipIdentity = "valid_vip_identity";

    //是否可在线客服退款
    public static final String canCustomerRefund = "can_customer_refund";

    //场景
    public static final String serviceHallVipEntrance = "serviceHallVipEntrance";

    //飞享会员客服大厅自助退款
    public static final String kfCanSelfRefund = "kfCanSelfRefund";
    //飞享会员客服大厅自助关闭续费
    public static final String kfCanSelfCloseRenew = "kfCanSelfCloseRenew";
    //飞享会员客服大厅自助取消扣款
    public static final String kfCanSelfCancelWithhold = "kfCanSelfCancelWithhold";
    //飞享会员客服大厅退款挽留
    public static final String kfSelfRefundRetain = "kfSelfRefundRetain";

    //飞跃会员客服大厅自助退款
    public static final String FYVipKfCanSelfRefund = "FYVipKfCanSelfRefund";
    //飞跃会员客服大厅自助取消扣款
    public static final String FYVipKfCanSelfCancelWithhold = "FYVipKfCanSelfCancelWithhold";

}