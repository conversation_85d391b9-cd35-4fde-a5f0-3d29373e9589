/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.config;

import com.xinfei.vocprod.biz.aop.DigestLogInterceptor;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.aop.support.JdkRegexpMethodPointcut;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 拦截器配置
 *
 * <AUTHOR>
 * @version $ DigestLogInterceptorConfig, v 0.1 2023/8/28 21:55 <PERSON><PERSON>.Huang Exp $
 */
@Configuration
public class InterceptorConfig {


    /**
     * 生成拦截切面
     *
     * @return  切面
     */
    @Bean
    public DefaultPointcutAdvisor digestLogPointcutAdvisor() {

        DigestLogInterceptor methodInterceptor = new DigestLogInterceptor();
        DefaultPointcutAdvisor advisor = new DefaultPointcutAdvisor();

        JdkRegexpMethodPointcut pointcut = new JdkRegexpMethodPointcut();
        pointcut.setPattern("com.xinfei.vocprod.biz.api.*");

        advisor.setPointcut(pointcut);
        advisor.setAdvice(methodInterceptor);

        return advisor;
    }
}
