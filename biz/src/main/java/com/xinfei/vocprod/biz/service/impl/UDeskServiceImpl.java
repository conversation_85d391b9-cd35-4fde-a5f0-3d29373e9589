/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskLinkRequest;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskResultDto;
import com.xinfei.vocprod.biz.config.VocProdApolloConfig;
import com.xinfei.vocprod.biz.enums.AppErrDtlEnum;
import com.xinfei.vocprod.biz.enums.AppException;
import com.xinfei.vocprod.biz.model.entity.RequestUser;
import com.xinfei.vocprod.biz.model.entity.UdeskConfBo;
import com.xinfei.vocprod.biz.service.UDeskService;
import com.xinfei.vocprod.biz.service.VipService;
import com.xinfei.vocprod.biz.service.common.CisCommonService;
import com.xinfei.vocprod.facade.rr.UDeskRequest;
import com.xinfei.vocprod.facade.rr.dto.Customer;
import com.xinfei.vocprod.itl.client.UDeskClient;
import com.xinfei.vocprod.itl.impl.CisFacadeClientImpl;
import com.xinfei.vocprod.itl.impl.VocmngFeignClientImpl;
import com.xinfei.vocprod.util.LogUtil;
import com.xinfei.vocprod.util.enums.MemberTypeEnum;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ UDeskServiceImpl, v 0.1 2024/7/8 14:10 wancheng.qu Exp $
 */
@Service
@Slf4j
public class UDeskServiceImpl implements UDeskService {
    private static final String APP_XYF = "xyf";
    private static final String APP_XYF01 = "xyf01";

    @Resource
    private CisFacadeClientImpl cisFacadeClient;
    @Resource
    private UDeskClient uDeskClient;
    @Resource
    private VocProdApolloConfig vocProdApolloConfig;
    @Resource
    private VipService vipService;

    @Resource
    private CisCommonService cisCommonService;

    @Value("${client.new.udesk-config}")
    private String newUDeskConfig;

    @Resource
    private VocmngFeignClientImpl vocmngFeignClientImpl;

    @Override
    public String getRsaUrl(UDeskRequest customer) {
        // 开关打开 重新设置用户webToken、uId、dialogueDesc
        if (vocProdApolloConfig.isCustNewEnabled()) {
            String webToken = customer.getCustomer().getWeb_token();
            PageResult<UserSearchDTO> currentUserCisResult = cisFacadeClient.queryUserList(null, null, webToken, 1, 1);
            if (Objects.isNull(currentUserCisResult) || CollectionUtils.isEmpty(currentUserCisResult.getList())) {
                log.warn("CIS user not found for webTokenUserNo: {}. Cannot determine mobile.", webToken);
            }
            UserSearchDTO currentUserCisInfo = currentUserCisResult.getList().get(0);
            String mobile = currentUserCisInfo.getMobile();

            if (StringUtils.isBlank(mobile)) {
                log.warn("Mobile number is blank for CIS user with webTokenUserNo: {}. Cannot find related accounts.", webToken);
                throw new RuntimeException("Mobile number is blank for CIS user with webTokenUserNo: " + webToken);
            }
            PageResult<UserSearchDTO> relatedCisUsersResult = cisFacadeClient.queryUserList(mobile, null, null, 1, 10);
            if (Objects.isNull(relatedCisUsersResult) || CollectionUtils.isEmpty(relatedCisUsersResult.getList())) {
                log.warn("No related CIS users found for mobile: {}", mobile);
            }
            Map<String, String> appUserNoMap = relatedCisUsersResult.getList().stream()
                    .filter(user -> StringUtils.isNotBlank(user.getApp()) && Objects.nonNull(user.getUserNo()))
                    .collect(Collectors.toMap(
                            UserSearchDTO::getApp,
                            user -> String.valueOf(user.getUserNo()),
                            (existingUserNo, newUserNo) -> existingUserNo
                    ));
            String currentApp = null;
            for (Map.Entry<String, String> entry : appUserNoMap.entrySet()) {
                if (webToken.equals(entry.getValue())) {
                    currentApp = entry.getKey();
                }
            }
            if (StringUtils.isBlank(currentApp)) {
                log.warn("Could not determine the app for the input webTokenUserNo: {} among users with mobile: {}. AppUserNoMap: {}",
                        webToken, mobile, appUserNoMap);
            }

            if (StringUtils.equals(APP_XYF, currentApp) && StringUtils.isNotBlank(appUserNoMap.get(APP_XYF01))) {
                customer.getCustomer().setC_cf_uid(appUserNoMap.get(APP_XYF01));
                customer.getCustomer().setWeb_token(appUserNoMap.get(APP_XYF01));
                customer.getCustomer().setC_cf_dialogueDesc(appUserNoMap.get(APP_XYF01));
                customer.getCustomer().setC_tags(vocmngFeignClientImpl.getUserLabel(Long.valueOf(appUserNoMap.get(APP_XYF01))));
            }
        }
        String webToken = customer.getCustomer().getWeb_token();
        String uDeskVip = null;
        // 查询是会员等级
        if (StringUtils.isNotBlank(webToken)) {
            // 不会存在既是飞享又是飞跃的情况，直接串行化判断
            if (vipService.isVip(webToken)) {
                uDeskVip = MemberTypeEnum.FEI_XIANG.getDescription();
            }
            if (vipService.isSuperVip(webToken)) {
                uDeskVip = MemberTypeEnum.FEI_YUE.getDescription();
            }
            customer.getCustomer().setC_cf_会员卡分流(Objects.nonNull(MemberTypeEnum.getByCode(vipService.vipClassifyGroup(webToken)))
                    ? MemberTypeEnum.getByCode(vipService.vipClassifyGroup(webToken)).getDescription() : MemberTypeEnum.NON_MEMBER.getDescription());
            customer.getCustomer().setC_cf_会员卡(uDeskVip);
        }
        return uDeskClient.getRsaUrl(customer, vocProdApolloConfig.isEncfUid());
    }

    @Override
    public HelpCenterGetUdeskResultDto getUdeskLink(HelpCenterGetUdeskLinkRequest request) {
        RequestUser requestUser = cisCommonService.getRequestUserByUserNo(Long.parseLong(request.getUserNo()));
        if (ObjectUtils.isEmpty(requestUser)) {
            throw new AppException(AppErrDtlEnum.USER_NOT_EXIST);
        }
        String url = getUdeskLinkFromProd(requestUser, request.getApp());
        if (ObjectUtils.isEmpty(url)) {
            throw new AppException(AppErrDtlEnum.UDESK_GET_FIELD);
        }
        HelpCenterGetUdeskResultDto helpCenterGetUdeskResultDto = new HelpCenterGetUdeskResultDto();
        helpCenterGetUdeskResultDto.setUrl(url);
        return helpCenterGetUdeskResultDto;
    }

    public String getUdeskLinkFromProd(RequestUser requestUser, String app) {
        UDeskRequest uDeskRequest = initUdeskRequest(requestUser.getUserNo().toString(), app);
        if (ObjectUtils.isEmpty(uDeskRequest)) {
            return null;
        }
        return getRsaUrl(uDeskRequest);
    }

    private UDeskRequest initUdeskRequest(String userNo, String app) {
        if (ObjectUtils.isEmpty(app)) {
            return null;
        }
        UdeskConfBo udeskConf = getUdeskConf(app);
        if (ObjectUtils.isEmpty(udeskConf)) {
            return null;
        }
        UDeskRequest uDeskRequest = new UDeskRequest();
        Customer customer = new Customer();
        customer.setWeb_token(userNo);
        customer.setC_cf_uid(userNo);
        customer.setC_cf_dialogueDesc(userNo);
//        uDeskRequest.setTraceId(AppRequestUtil.getRequestId());
        uDeskRequest.setUrlBase(udeskConf.getUrl());
        uDeskRequest.setUpbKey(udeskConf.getPublicKey());
        uDeskRequest.setImUserKey(udeskConf.getImUserKey());
        uDeskRequest.setCustomer(customer);
        return uDeskRequest;
    }

    private UdeskConfBo getUdeskConf(String app) {
        try {
            Map<String, UdeskConfBo> confBoMap;
            confBoMap = JsonUtil.parseJson(newUDeskConfig, new TypeReference<Map<String, UdeskConfBo>>() {
            });

            if (ObjectUtils.isEmpty(confBoMap) || !confBoMap.containsKey(app)) {
                return null;
            }
            return confBoMap.get(app);
        } catch (Exception e) {
            log.info(LogUtil.clientLog("getUdeskConf-error", new HashMap<String, Object>() {{
                put("app", app);
                put("err", e.getMessage());
            }}));
        }
        return null;
    }
}