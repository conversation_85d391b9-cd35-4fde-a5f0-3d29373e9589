/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.service.impl;

import com.xinfei.vocprod.biz.common.RandomBizKey;
import com.xinfei.vocprod.biz.service.CommonService;
import com.xinfei.vocprod.facade.rr.GetABRequest;
import com.xinfei.vocprod.itl.impl.RandomGeneratorClientImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * @version $ CommonServiceImpl, v 0.1 2024-12-20 14:03 junjie.yan Exp $
 */
@Service
public class CommonServiceImpl implements CommonService {

    @Resource
    private RandomGeneratorClientImpl randomGeneratorClient;


    @Override
    public String getGroupAB(GetABRequest request) {
        return randomGeneratorClient.ab(request.getUserNo(), request.getBizKey());
    }
}