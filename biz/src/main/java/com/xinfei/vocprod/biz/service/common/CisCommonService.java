/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.service.common;

import com.xinfei.vocprod.biz.enums.AppErrDtlEnum;
import com.xinfei.vocprod.biz.enums.AppException;
import com.xinfei.vocprod.biz.model.entity.RequestUser;
import com.xinfei.vocprod.itl.impl.CisFacadeClientImpl;
import com.xyf.cis.query.facade.dto.standard.response.RegisterInfoDTO;
import com.xyf.user.auth.dto.nonStandard.response.AuthIdcardDTO;
import com.xyf.user.auth.dto.request.QueryLatestPassAuthIdCardRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version $ CisCommonService, v 0.1 2024-11-07 19:15 junjie.yan Exp $
 */
@Service
public class CisCommonService {

    @Resource
    private CisFacadeClientImpl cisFacadeClient;

    public RequestUser getRequestUserByUserNo(Long userNo) {
        if (ObjectUtils.isEmpty(userNo)) {
            return null;
        }
        RequestUser requestUser = new RequestUser();

        //获取注册信息
        RegisterInfoDTO registerInfoDTO = cisFacadeClient.queryRegisterInfoByUserNo(userNo);
        if (ObjectUtils.isEmpty(registerInfoDTO)) {
            throw new AppException(AppErrDtlEnum.USER_NOT_EXIST);
        }
        requestUser.setUserNo(userNo);
        requestUser.setApp(registerInfoDTO.getApp());
        requestUser.setCustNo(registerInfoDTO.getCustNo());
        requestUser.setIdNo(registerInfoDTO.getIdCardNumber());
        requestUser.setName(registerInfoDTO.getCustName());
        requestUser.setMobile(registerInfoDTO.getMobile());
        requestUser.setOldUserNo(registerInfoDTO.getOldUserNo());
        requestUser.setIsLogin(true);

        //如果用户授信未成功，未绑定的时候，获取ocr数据
        if (StringUtils.isEmpty(registerInfoDTO.getCustNo())) {
            QueryLatestPassAuthIdCardRequest request = new QueryLatestPassAuthIdCardRequest();
            request.setUserNo(userNo);
            AuthIdcardDTO authIdcardDTO = cisFacadeClient.queryLatestPassAuthIdCard(request);
            if (Objects.nonNull(authIdcardDTO)) {
                requestUser.setCustNo(authIdcardDTO.getCustNo());
                requestUser.setIdNo(authIdcardDTO.getIdNo());
                requestUser.setName(authIdcardDTO.getName());
            }
        }

        return requestUser;
    }

}