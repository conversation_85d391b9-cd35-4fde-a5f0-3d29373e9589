/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskLinkRequest;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskResultDto;
import com.xinfei.vocprod.biz.common.TechplayConstants;
import com.xinfei.vocprod.biz.log.DigestLogAnnotated;
import com.xinfei.vocprod.biz.log.QueryDigestLog;
import com.xinfei.vocprod.biz.service.UDeskService;
import com.xinfei.vocprod.biz.template.QueryCallBack;
import com.xinfei.vocprod.biz.template.QueryTemplate;
import com.xinfei.vocprod.biz.util.ParamCheckUtil;
import com.xinfei.vocprod.facade.UDeskFacade;
import com.xinfei.vocprod.facade.rr.*;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.xinfei.vocprod.biz.util.ParamCheckUtil.*;

@RestController
@RequestMapping("/udesk")
@Slf4j
public class UDeskFacadeImpl implements UDeskFacade {

    @Autowired
    private UDeskService uDeskService;

    @Override
    @PostMapping("/getRsaUrl")
    @DigestLogAnnotated(TechplayConstants.TRADE_DIGEST_LOG)
    public UDeskResponse getRsaUrl(@RequestBody UDeskRequest request) {

        final UDeskResponse result = new UDeskResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getCustomer(), USER_REQUEST_USER);
                ParamCheckUtil.checkParamNotBlank(request.getCustomer().getWeb_token(), "web_token");
                ParamCheckUtil.checkParamNotBlank(request.getCustomer().getC_cf_uid(), "c_cf_uid");
                ParamCheckUtil.checkParamNotBlank(request.getCustomer().getC_cf_dialogueDesc(), "c_cf_dialogueDesc");
                ParamCheckUtil.checkParamNotBlank(request.getUrlBase(), "url");
                ParamCheckUtil.checkParamNotBlank(request.getUpbKey(), "publicKey");
                ParamCheckUtil.checkParamNotBlank(request.getImUserKey(), "imUserKey");
            }

            @Override
            public void doQuery() {
                String url = uDeskService.getRsaUrl(request);
                log.info("getRsaUrl traceId:{},req:{},result:{}", request.getTraceId(), JsonUtil.toJson(request), url);
                result.setSuc(Boolean.TRUE);
                result.setUrl(url);
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }

    @PostMapping("/getNewUDeskLink")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public HelpCenterGetUdeskResultDto getNewUDeskLink(@RequestBody HelpCenterGetUdeskLinkRequest request) {
        final HelpCenterGetUdeskResultDto result = new HelpCenterGetUdeskResultDto();

        QueryTemplate.query(result, new QueryCallBack() {
            @Override
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, USER_REQUEST);
                ParamCheckUtil.checkParamNotNull(request.getUserNo(), USER_REQUEST_USER);
            }

            @Override
            public void doQuery() {
                HelpCenterGetUdeskResultDto resp = uDeskService.getUdeskLink(request);
                log.info("getNewUDeskLink,req:{},result:{}", JsonUtil.toJson(request), resp);
                result.setSuc(Boolean.TRUE);
                result.setUrl(resp.getUrl());
            }

            @Override
            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(JsonUtil.toJson(request));
            }
        });

        return result;
    }


}
