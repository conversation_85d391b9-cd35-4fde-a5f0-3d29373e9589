/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.log;

import com.xinfei.vocprod.facade.rr.UserRequest;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 * @version $ QueryDigestLog, v 0.1 2023/8/28 20:32 <PERSON><PERSON>.Huang Exp $
 */
@Setter
final public class TradeDigestLog extends BaseDigestLog {

    /**
     * 用户名
     */
    private String userName;

    /**
     * 应用名
     */
    private String appName;

    /**
     * 构造器
     *
     * @param userRequest 用户请求
     */
    public TradeDigestLog(UserRequest userRequest) {
        if (userRequest == null || userRequest.getUser() == null) {
            return;
        }
        this.userName = userRequest.getUser().getName();
    }

    /**
     * 构造器
     *
     * @param appName
     */
    public TradeDigestLog(String appName) {
        if (appName == null) {
            return;
        }
        this.appName = appName;
    }

    /**
     * 日志业务信息组装
     *
     * @param log 日志信息
     */
    protected void composeTransInfo(Map<String,String> log) {
        log.put("userName", userName);
        log.put("appName", appName);
    }

}
