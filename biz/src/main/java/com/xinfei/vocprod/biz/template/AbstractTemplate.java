/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import com.xinfei.vocprod.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocprod.biz.model.enums.TechplayErrScenarioEnum;
import com.xinfei.vocprod.biz.model.exception.TechplayException;
import com.xinfei.vocprod.biz.util.ErrorContextUtil;
import com.xinfei.xfframework.common.BaseResponse;
import com.xinfei.xfframework.common.ErrorContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 处理模版基类
 *
 * <AUTHOR>
 * @version $ AbstractTemplate, v 0.1 2023/8/28 16:48 Jinyan.Huang Exp $
 */
public class AbstractTemplate {

    /** logger */
    protected static final Logger LOGGER = LoggerFactory.getLogger(AbstractTemplate.class);

    /**
     * 构建处理成功的返回结果
     *
     * @param response   处理结果
     */
    protected static void buildSuccessResponse(BaseResponse response) {
        response.setSuc(true);
    }

    /**
     * 构建处理失败的返回结果
     *
     * @param response          处理结果
     * @param scenarioEnum      错误场景
     * @param transException    业务异常
     */
    protected static void buildFailureResponse(BaseResponse response,
                                               TechplayErrScenarioEnum scenarioEnum,
                                               TechplayException transException) {
        response.setSuc(false);
        ErrorContext errorContext = ErrorContextUtil.genErrorContext(scenarioEnum,
                transException);
        response.setErrorContext(errorContext);
//        response.setCode(errorContext.getErrCode());
//        response.setMsg(errorContext.getErrDesc());
    }

    /**
     * 构造失败的结果对象
     *
     * @param response      处理结果
     * @param scenarioEnum  错误场景
     * @param detailEnum    错误明细枚举
     * @param errorMsg      错误信息
     */
    protected static void buildFailureResponse(BaseResponse response,
                                             TechplayErrScenarioEnum scenarioEnum,
                                             TechplayErrDtlEnum detailEnum, String errorMsg) {
        response.setSuc(false);
        ErrorContext errorContext = ErrorContextUtil.genErrorContext(scenarioEnum, detailEnum,
                errorMsg);
        response.setErrorContext(errorContext);
    }
}
