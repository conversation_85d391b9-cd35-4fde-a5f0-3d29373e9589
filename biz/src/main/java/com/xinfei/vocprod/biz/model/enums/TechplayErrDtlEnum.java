/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.model.enums;

/**
 * 错误码明显段定义枚举
 *
 * <p>本枚举的code对应于标准错误码10~12位。
 * 而errorLevel对应于标准错误码的第4位
 *
 * <p>在标准错误码的位置如下：
 *     <table border="1">
 *     <tr>
 *     <td>位置</td><td>1</td><td>2</td><td>3</td><td bgcolor="yellow">4</td><td>5</td><td>6</td><td>7</td><td>8</td><td>9</td><td bgcolor="red">10</td><td bgcolor="red">11</td><td bgcolor="red">12</td>
 *     </tr>
 *     <tr>
 *     <td>示例</td><td>X</td><td>E</td><td>0</td><td>1</td><td>0</td><td>1</td><td>0</td><td>1</td><td>1</td><td>0</td><td>2</td><td>7</td>
 *     </tr>
 *     <tr>
 *     <td>说明</td><td colspan=2>固定<br>标识</td><td>规<br>范<br>版<br>本</td><td>错<br>误<br>级<br>别</td><td>错<br>误<br>类<br>型</td><td colspan=4>错误场景</td><td colspan=3>错误编<br>码</td>
 *     </tr>
 *     </table>
 *
 * <p>错误明细码的CODE取值空间如下：
 * <ul>
 *     <li>公共类错误码[000-099,999]
 *     <li>事务管理类错误码[100-149]
 *     <li>支用还款等交易类错误码[150-245]
 *     <li>日终处理类错误码[250-299]
 *     <li>查询类错误码[300-349]
 *     <li>管理类错误码[350-399]
 * </ul>
 *
 * <AUTHOR>
 * @version $ TechplayErrDtlEnum, v 0.1 2023/8/28 17:42 Jinyan.Huang Exp $
 */
public enum TechplayErrDtlEnum {

    //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\//
    //                      公共类错误码[000-099,999]                             //
    //\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\//
    /**
     * 其它未知异常
     */
    UNKNOWN_EXCEPTION("999", ErrorLevelsEnum.ERROR, "其它未知异常"),

    /**
     * 配置错误
     */
    CONFIGURATION_ERROR("001", ErrorLevelsEnum.FATAL, "配置错误"),

    /**
     * 数据库异常
     */
    DB_EXCEPTION("002", ErrorLevelsEnum.ERROR, "数据库异常"),

    /**
     * 数据更新异常
     */
    DATA_UPDATE_EXCEPTION("003", ErrorLevelsEnum.ERROR, "数据库异常"),

    //========================================================================//
    //                              业务处理类                                  //
    //========================================================================//
    /**
     * 服务调用请求信息不合法
     */
    REQ_PARAM_NOT_VALID("101", ErrorLevelsEnum.WARN, "服务调用请求信息不合法"),


    ;

    // ~~~ 属性定义 ~~~

    /**
     * 枚举编码
     */
    private final String code;

    /**
     * 错误级别
     */
    private final ErrorLevelsEnum errorLevel;

    /**
     * 描述说明
     */
    private final String description;

    /**
     * 私有构造函数。
     *
     * @param code        枚举编码
     * @param errorLevel  错误级别
     * @param description 描述说明
     */
    private TechplayErrDtlEnum(String code, ErrorLevelsEnum errorLevel, String description) {
        this.code = code;
        this.errorLevel = errorLevel;
        this.description = description;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    // ~~~容器方法 ~~~

    /**
     * @return Returns the description.
     */
    public String getDescription() {
        return description;
    }

    /**
     * Getter method for property <tt>errorLevvel</tt>.
     *
     * @return property value of errorLevvel
     */
    public ErrorLevelsEnum getErrorLevel() {
        return errorLevel;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code 枚举编码
     * @return 支付错误明细枚举
     */
    public static TechplayErrDtlEnum getByCode(String code) {
        for (TechplayErrDtlEnum detailCode : values()) {
            if (detailCode.getCode().equals(code)) {

                return detailCode;
            }
        }
        return null;
    }
}
