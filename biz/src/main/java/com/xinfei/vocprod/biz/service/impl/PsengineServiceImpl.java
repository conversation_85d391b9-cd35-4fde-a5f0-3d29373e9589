/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.service.impl;

import cn.hutool.core.lang.UUID;
import com.xinfei.cashiercore.common.service.facade.request.management.WhitelistMarkRequest;
import com.xinfei.common.lang.context.BizTraceContext;
import com.xinfei.psenginecore.facade.rr.request.VocKeywordsRequest;
import com.xinfei.vocprod.biz.config.VocProdApolloConfig;
import com.xinfei.vocprod.biz.service.PsengineService;
import com.xinfei.vocprod.biz.service.VipService;
import com.xinfei.vocprod.facade.Constants;
import com.xinfei.vocprod.facade.request.VocKeyRequest;
import com.xinfei.vocprod.facade.response.CustomCardContent;
import com.xinfei.vocprod.facade.response.VocKeyResponse;
import com.xinfei.vocprod.itl.CashiercoreFeignClient;
import com.xinfei.vocprod.itl.PsengineFeinClient;
import com.xinfei.vocprod.itl.impl.CisFacadeClientImpl;
import com.xinfei.vocprod.itl.impl.NPayServiceImpl;
import com.xinfei.vocprod.itl.impl.VocmngFeignClientImpl;
import com.xinfei.vocprod.biz.template.CustomCardBuilder;
import com.xinfei.vocprod.biz.template.HtmlTemplateManager;
import com.xinfei.vocprod.itl.rr.BillDetailDto;
import com.xinfei.vocprod.itl.rr.IVRLatestBillDto;
import com.xinfei.vocprod.itl.rr.IVRLoanSettlementDto;
import com.xinfei.vocprod.itl.rr.IVRMonthlyAmountDto;
import com.xinfei.vocprod.itl.rr.IVROrderInfoDto;
import com.xinfei.vocprod.itl.rr.MembershipCardDto;
import com.xinfei.vocprod.itl.rr.PublicAccountInfo;
import com.xinfei.vocprod.itl.rr.PublicAccountRequest;
import com.xinfei.vocprod.itl.rr.UdeskSendMessageRequest;
import com.xinfei.vocprod.itl.rr.UserNoAppDto;
import com.xinfei.vocprod.util.enums.ErrDtlEnum;
import com.xinfei.vocprod.util.enums.MemberTypeEnum;
import com.xinfei.vocprod.util.exception.VocprodException;
import com.xinfei.vocprod.util.threadpool.ContextInheritableThreadPoolExecutor;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.user.facade.common.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * udesk接口文档地址：https://www.udesk.cn/doc/robot/webhook  请注意，customerExtra.customerId 接口定义integer不满足要求，需改成long
 *
 * <AUTHOR>
 * @version $ PsengineServiceImpl, v 0.1 2024/4/18 11:59 wancheng.qu Exp $
 */
@Slf4j
@Service
public class PsengineServiceImpl implements PsengineService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE;
    private static final Executor spmExecutor = new ContextInheritableThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 2,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadFactory() {
                private final ThreadGroup threadGroup = new ThreadGroup("PsengineServiceThreadPool");
                private final AtomicInteger threadNumber = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(threadGroup, r, "PsengineService-thread-pool-" + threadNumber.getAndIncrement());
                }},
            new ThreadPoolExecutor.AbortPolicy()
    );

    @Resource
    private PsengineFeinClient psengineFeinClient;
    @Resource
    private CashiercoreFeignClient cashiercoreFeignClient;
    @Resource
    private VocProdApolloConfig vocProdApolloConfig;
    @Resource
    private NPayServiceImpl nPayService;
    @Autowired
    private VocmngFeignClientImpl vocmngFeignClientImpl;
    @Resource
    private CisFacadeClientImpl cisFacadeClient;
    @Resource
    private VipService vipService;
    @Autowired
    private CustomCardBuilder customCardBuilder;

    @Override
    public VocKeyResponse vocKeywordsSet(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        ImmutableTriple<Boolean, Long, String> b = validReq(req, resp);
        if (b.getLeft()) {
            return resp;
        }
        VocKeywordsRequest v = getReq(b.getMiddle(), b.getRight());
        if (vocProdApolloConfig.isCashOpenFlag()) {
            CompletableFuture.runAsync(() -> whitelistMark(v), spmExecutor);
        }
        Boolean saveSuccess = psengineFeinClient.vocKeywordsSet(v);
        setErrorResponse(resp, saveSuccess ? 0 : -1, saveSuccess ? "Success" : "Save fail");
        resp.setMessage(new VocKeyResponse.Message());
        return resp;
    }

    @Override
    public VocKeyResponse queryPublicAccountInfo(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        PublicAccountRequest publicAccountRequest = new PublicAccountRequest();
        String userNo = req.getCustomerExtra().getDialogueDesc();
        if (StringUtils.isNotBlank(userNo)) {
            publicAccountRequest.setUserNo(userNo);
        }
        PublicAccountInfo publicAccountInfo = nPayService.queryPublicAccountInfo(publicAccountRequest);
        setErrorResponse(resp, publicAccountInfo != null ? 0 : -1, publicAccountInfo != null ? "Success" : "No account info found");
        Map<String, Object> variables = new HashMap<>();
        variables.put("bankCardNo", publicAccountInfo.getBankCardNo());
        variables.put("bankAccountName", publicAccountInfo.getBankAccountName());
        variables.put("bankName", publicAccountInfo.getBankName());
        variables.put("openCityName", publicAccountInfo.getOpenCityName());
        variables.put("openBankBranch", publicAccountInfo.getOpenBankBranch());
        variables.put("openBankNo", publicAccountInfo.getOpenBankNo());
        resp.setVariables(variables);
        resp.setMessage(new VocKeyResponse.Message());
        return resp;
    }

    @Override
    public VocKeyResponse getUserStatus(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        resp.setResCode(0);
        int userStatus = 0;
        if (Objects.nonNull(req.getCustomerExtra()) && StringUtils.isNotBlank(req.getCustomerExtra().getCallerNumber())) {
            try {
                userStatus = vocmngFeignClientImpl.getUserStatus(req.getCustomerExtra().getCallerNumber()) ? 1 : 0;
            } catch (Exception e) {
                resp.setResCode(-1);
                resp.setResError(e.getMessage());
            }
        } else {
            resp.setResCode(-1);
            resp.setResError("缺少必要参数：手机号");
        }
        Map<String, Object> variables = new HashMap<>();
        variables.put("userStatus", userStatus);
        resp.setVariables(variables);
        resp.setMessage(new VocKeyResponse.Message());
        return resp;
    }

    @Override
    public VocKeyResponse queryIVROrderList(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        resp.setResCode(0);
        if (Objects.nonNull(req.getCustomerExtra()) && StringUtils.isNotBlank(req.getCustomerExtra().getCallerNumber())
                && StringUtils.isNotBlank(req.getCustomerExtra().getOtherCallerNumber())) {
            try {
                String callerMobile = req.getCustomerExtra().getCallerNumber();
                String mobile = req.getCustomerExtra().getOtherCallerNumber();
                log.info("开始查询手机号关联的用户信息, 手机号: {}", mobile);
                // 根据手机号查询关联的用户信息
                PageResult<UserSearchDTO> relatedCisUsersResult = cisFacadeClient.queryUserList(mobile, null, null, 1, 10);
                if (Objects.isNull(relatedCisUsersResult) || CollectionUtils.isEmpty(relatedCisUsersResult.getList())) {
                    log.warn("No related CIS users found for mobile: {}", mobile);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到关联用户信息");
                    return resp;
                }
                // 找customerNo
                String customerNo = relatedCisUsersResult.getList().stream()
                        .filter(e -> StringUtils.isNotBlank(e.getCustNo()))
                        .findAny()
                        .map(UserSearchDTO::getCustNo)
                        .orElse(null);

                log.info("找到的应用用户映射: {}", customerNo);
                // 如果没有找到xyf或xyf01应用下的用户
                if (StringUtils.isBlank(customerNo)) {
                    log.warn("No user found for mobile: {}", mobile);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到customerNo");
                    return resp;
                }

                // 调用服务查询IVR订单信息
                IVROrderInfoDto ivrOrderInfoDto = vocmngFeignClientImpl.queryIVROrderList(customerNo, callerMobile);
                if (Objects.nonNull(ivrOrderInfoDto)) {
                    Map<String, Object> variables = new HashMap<>();
                    // 将日期和金额转换为String类型
                    variables.put("latestBillDate", ivrOrderInfoDto.getLatestBillDate() != null ? ivrOrderInfoDto.getLatestBillDate().format(DATE_FORMATTER) : "");
                    variables.put("latestOrderAmount", ivrOrderInfoDto.getLatestOrderAmount() != null ? ivrOrderInfoDto.getLatestOrderAmount().toString() : "");
                    variables.put("totalMonthlyAmount", ivrOrderInfoDto.getTotalMonthlyAmount() != null ? ivrOrderInfoDto.getTotalMonthlyAmount().toString() : "");
                    variables.put("totalOverdueAmount", ivrOrderInfoDto.getTotalOverdueAmount() != null ? ivrOrderInfoDto.getTotalOverdueAmount().toString() : "");
                    resp.setVariables(variables);
                    resp.setMessage(new VocKeyResponse.Message());
                } else {
                    log.warn("No IVR order information found for customerNo: {}", customerNo);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到订单信息");
                }
            } catch (Exception e) {
                log.error("查询IVR订单信息异常", e);
                resp.setResCode(-1);
                resp.setResError(e.getMessage());
            }
        } else {
            resp.setResCode(-1);
            resp.setResError("缺少必要参数：手机号");
        }
        return resp;
    }

    @Override
    public VocKeyResponse verifyIdCardLast6(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        resp.setResCode(0);
        if (Objects.nonNull(req.getCustomerExtra()) && StringUtils.isNotBlank(req.getCustomerExtra().getCallerNumber())) {
            try {
                String idCardLast6 = StringUtils.EMPTY;
                if (Objects.nonNull(req.getVariables())
                        && Objects.nonNull(req.getVariables().get("idCardLast6"))) {
                    idCardLast6 = req.getVariables().get("idCardLast6").toString();
                }

                // 判断idCardLast6是否是6位字符串
                if (StringUtils.isEmpty(idCardLast6) || idCardLast6.length() != 6) {
                    Map<String, Object> variables = new HashMap<>();
                    variables.put("verifyIdCardLast6", 0);
                    resp.setVariables(variables);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("身份证后6位必须是6位字符");
                    return resp;
                }

                if (idCardLast6 != null && idCardLast6.length() > 0 && idCardLast6.endsWith("*")) {
                    // 替换最后一位为X
                    idCardLast6 = idCardLast6.substring(0, idCardLast6.length() - 1) + "X";
                }

                // 检查是否包含有效字符（前5位必须是数字，最后一位可以是数字或大写X）
                String firstFive = idCardLast6.substring(0, 5);
                char lastChar = idCardLast6.charAt(5);
                if (!firstFive.matches("\\d{5}") || !(Character.isDigit(lastChar) || lastChar == 'X' || lastChar == 'x')) {
                    Map<String, Object> variables = new HashMap<>();
                    variables.put("verifyIdCardLast6", 0);
                    resp.setVariables(variables);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("身份证后6位格式不正确，前5位必须是数字，最后一位可以是数字或X");
                    return resp;
                }

                // 标准化输入，将小写x转换为大写X
                if (lastChar == 'x') {
                    idCardLast6 = firstFive + 'X';
                }

                // 验证身份证后6位是否匹配
                log.info("开始验证身份证后6位, 手机号: {}, 身份证后6位: {}",
                        req.getCustomerExtra().getCallerNumber(), idCardLast6);
                Boolean verifyResult = vocmngFeignClientImpl.verifyIdCardLast6(
                        req.getCustomerExtra().getCallerNumber(), idCardLast6);
                log.info("身份证后6位验证结果: {}", verifyResult);

                Map<String, Object> variables = new HashMap<>();
                variables.put("verifyIdCardLast6", verifyResult ? 1 : 0);
                resp.setVariables(variables);
                resp.setMessage(new VocKeyResponse.Message());
            } catch (Exception e) {
                log.error("验证身份证后6位异常", e);
                resp.setResCode(-1);
                resp.setResError(e.getMessage());
            }
        } else {
            resp.setResCode(-1);
            resp.setResError("缺少必要参数：手机号");
        }
        return resp;
    }

    @Override
    public VocKeyResponse queryUserVipStatus(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        String callerNumber = req.getCustomerExtra().getCallerNumber();
        String userNo = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(callerNumber)) {
            List<UserNoAppDto> userNoList = vocmngFeignClientImpl.getUserNoList(callerNumber);
            if (CollectionUtils.isNotEmpty(userNoList)) {
                UserNoAppDto currentUser = userNoList.get(0);
                userNo = StringUtils.isNotBlank(currentUser.getUserNo()) ? currentUser.getUserNo() : StringUtils.EMPTY;
            }
        }
        log.info("PsengineServiceImpl#queryUserVipStatus,callerNumber:{},userNo:{}", callerNumber, userNo);
        String uDeskVip = MemberTypeEnum.NON_MEMBER.getCode();
        if (StringUtils.isNotBlank(userNo)) {
            // 不会存在既是飞享又是飞跃的情况，直接串行化判断
            if (vipService.isVip(userNo)) {
                uDeskVip = MemberTypeEnum.FEI_XIANG.getCode();
            }
            if (vipService.isSuperVip(userNo)) {
                uDeskVip = MemberTypeEnum.FEI_YUE.getCode();
            }
        }
        log.info("PsengineServiceImpl#queryUserVipStatus,uDeskVip:{}", uDeskVip);
        Map<String, Object> variables = new HashMap<>();
        variables.put("userVipStatus", uDeskVip);
        resp.setVariables(variables);
        resp.setMessage(new VocKeyResponse.Message());
        return resp;
    }

    @Override
    public VocKeyResponse queryVipClassifyGroup(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        String callerNumber = req.getCustomerExtra().getCallerNumber();
        String userNo = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(callerNumber)) {
            List<UserNoAppDto> userNoList = vocmngFeignClientImpl.getUserNoList(callerNumber);
            if (CollectionUtils.isNotEmpty(userNoList)) {
                UserNoAppDto currentUser = userNoList.get(0);
                userNo = StringUtils.isNotBlank(currentUser.getUserNo()) ? currentUser.getUserNo() : StringUtils.EMPTY;
            }
        }
        log.info("PsengineServiceImpl#queryVipClassifyGroup,callerNumber:{},userNo:{}", callerNumber, userNo);
        String uDeskVip = MemberTypeEnum.NON_MEMBER.getCode();
        if (StringUtils.isNotBlank(userNo)) {
            uDeskVip = vipService.vipClassifyGroup(userNo);
        }
        log.info("PsengineServiceImpl#queryVipClassifyGroup,uDeskVip:{}", uDeskVip);
        Map<String, Object> variables = new HashMap<>();
        variables.put("vipClassifyGroup", uDeskVip);
        resp.setVariables(variables);
        resp.setMessage(new VocKeyResponse.Message());
        return resp;
    }

    @Override
    public VocKeyResponse queryIVRLatestBill(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        resp.setResCode(0);
        if (Objects.nonNull(req.getCustomerExtra()) && StringUtils.isNotBlank(req.getCustomerExtra().getCallerNumber())
                && StringUtils.isNotBlank(req.getCustomerExtra().getOtherCallerNumber())) {
            try {
                String callerMobile = req.getCustomerExtra().getCallerNumber();
                String mobile = req.getCustomerExtra().getOtherCallerNumber();
                log.info("开始查询手机号关联的用户信息, 手机号: {}", mobile);
                // 根据手机号查询关联的用户信息
                PageResult<UserSearchDTO> relatedCisUsersResult = cisFacadeClient.queryUserList(mobile, null, null, 1, 10);
                if (Objects.isNull(relatedCisUsersResult) || CollectionUtils.isEmpty(relatedCisUsersResult.getList())) {
                    log.warn("No related CIS users found for mobile: {}", mobile);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到关联用户信息");
                    return resp;
                }
                // 找customerNo
                String customerNo = relatedCisUsersResult.getList().stream()
                        .filter(e -> StringUtils.isNotBlank(e.getCustNo()))
                        .findAny()
                        .map(UserSearchDTO::getCustNo)
                        .orElse(null);

                log.info("找到的应用用户映射: {}", customerNo);
                // 如果没有找到xyf或xyf01应用下的用户
                if (StringUtils.isBlank(customerNo)) {
                    log.warn("No user found for mobile: {}", mobile);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到customerNo");
                    return resp;
                }

                // 调用服务查询IVR最新账单信息
                IVRLatestBillDto ivrLatestBillDto = vocmngFeignClientImpl.queryIVRLatestBill(customerNo, callerMobile);
                if (Objects.nonNull(ivrLatestBillDto)) {
                    Map<String, Object> variables = new HashMap<>();
                    // 将日期和金额转换为String类型
                    variables.put("latestBillDate", ivrLatestBillDto.getLatestBillDate() != null ? ivrLatestBillDto.getLatestBillDate().format(DATE_FORMATTER) : "");
                    variables.put("latestOrderAmount", ivrLatestBillDto.getLatestOrderAmount() != null ? ivrLatestBillDto.getLatestOrderAmount().toString() : "");
                    variables.put("totalOverdueAmount", ivrLatestBillDto.getTotalOverdueAmount() != null ? ivrLatestBillDto.getTotalOverdueAmount().toString() : "");
                    variables.put("hasReductionPlan", ivrLatestBillDto.getHasReductionPlan() != null ? ivrLatestBillDto.getHasReductionPlan().toString() : "false");
                    variables.put("planCount", ivrLatestBillDto.getPlanCount() != null ? ivrLatestBillDto.getPlanCount().toString() : "0");
                    variables.put("planTotalAmount", ivrLatestBillDto.getPlanTotalAmount() != null ? ivrLatestBillDto.getPlanTotalAmount().toString() : "");
                    variables.put("earliestPlanDueDate", ivrLatestBillDto.getEarliestPlanDueDate() != null ? ivrLatestBillDto.getEarliestPlanDueDate().format(DATE_FORMATTER) : "");
                    resp.setVariables(variables);
                    resp.setMessage(new VocKeyResponse.Message());
                } else {
                    log.warn("No IVR latest bill information found for customerNo: {}", customerNo);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到最新账单信息");
                }
            } catch (Exception e) {
                log.error("查询IVR最新账单信息异常", e);
                resp.setResCode(-1);
                resp.setResError(e.getMessage());
            }
        } else {
            resp.setResCode(-1);
            resp.setResError("缺少必要参数：手机号");
        }
        return resp;
    }

    @Override
    public VocKeyResponse queryIVRMonthlyAmount(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        resp.setResCode(0);
        if (Objects.nonNull(req.getCustomerExtra()) && StringUtils.isNotBlank(req.getCustomerExtra().getCallerNumber())
                && StringUtils.isNotBlank(req.getCustomerExtra().getOtherCallerNumber())) {
            try {
                String callerMobile = req.getCustomerExtra().getCallerNumber();
                String mobile = req.getCustomerExtra().getOtherCallerNumber();
                log.info("开始查询手机号关联的用户信息, 手机号: {}", mobile);
                // 根据手机号查询关联的用户信息
                PageResult<UserSearchDTO> relatedCisUsersResult = cisFacadeClient.queryUserList(mobile, null, null, 1, 10);
                if (Objects.isNull(relatedCisUsersResult) || CollectionUtils.isEmpty(relatedCisUsersResult.getList())) {
                    log.warn("No related CIS users found for mobile: {}", mobile);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到关联用户信息");
                    return resp;
                }
                // 找customerNo
                String customerNo = relatedCisUsersResult.getList().stream()
                        .filter(e -> StringUtils.isNotBlank(e.getCustNo()))
                        .findAny()
                        .map(UserSearchDTO::getCustNo)
                        .orElse(null);

                log.info("找到的应用用户映射: {}", customerNo);
                // 如果没有找到xyf或xyf01应用下的用户
                if (StringUtils.isBlank(customerNo)) {
                    log.warn("No user found for mobile: {}", mobile);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到customerNo");
                    return resp;
                }

                // 调用服务查询IVR当月应还金额信息
                IVRMonthlyAmountDto ivrMonthlyAmountDto = vocmngFeignClientImpl.queryIVRMonthlyAmount(customerNo, callerMobile);
                if (Objects.nonNull(ivrMonthlyAmountDto)) {
                    Map<String, Object> variables = new HashMap<>();
                    // 将金额转换为String类型
                    variables.put("totalMonthlyAmount", ivrMonthlyAmountDto.getTotalMonthlyAmount() != null ? ivrMonthlyAmountDto.getTotalMonthlyAmount().toString() : "");
                    variables.put("hasReductionPlan", ivrMonthlyAmountDto.getHasReductionPlan() != null ? ivrMonthlyAmountDto.getHasReductionPlan().toString() : "false");
                    variables.put("planCount", ivrMonthlyAmountDto.getPlanCount() != null ? ivrMonthlyAmountDto.getPlanCount().toString() : "0");
                    variables.put("planTotalAmount", ivrMonthlyAmountDto.getPlanTotalAmount() != null ? ivrMonthlyAmountDto.getPlanTotalAmount().toString() : "");
                    variables.put("earliestPlanDueDate", ivrMonthlyAmountDto.getEarliestPlanDueDate() != null ? ivrMonthlyAmountDto.getEarliestPlanDueDate().format(DATE_FORMATTER) : "");
                    resp.setVariables(variables);
                    resp.setMessage(new VocKeyResponse.Message());
                } else {
                    log.warn("No IVR monthly amount information found for customerNo: {}", customerNo);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到当月应还金额信息");
                }
            } catch (Exception e) {
                log.error("查询IVR当月应还金额信息异常", e);
                resp.setResCode(-1);
                resp.setResError(e.getMessage());
            }
        } else {
            resp.setResCode(-1);
            resp.setResError("缺少必要参数：手机号");
        }
        return resp;
    }

    @Override
    public VocKeyResponse queryIVRLoanSettlement(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        resp.setResCode(0);
        if (Objects.nonNull(req.getCustomerExtra()) && StringUtils.isNotBlank(req.getCustomerExtra().getCallerNumber())
                && StringUtils.isNotBlank(req.getCustomerExtra().getOtherCallerNumber())) {
            try {
                String callerMobile = req.getCustomerExtra().getCallerNumber();
                String mobile = req.getCustomerExtra().getOtherCallerNumber();
                log.info("开始查询手机号关联的用户信息, 手机号: {}", mobile);
                // 根据手机号查询关联的用户信息
                PageResult<UserSearchDTO> relatedCisUsersResult = cisFacadeClient.queryUserList(mobile, null, null, 1, 10);
                if (Objects.isNull(relatedCisUsersResult) || CollectionUtils.isEmpty(relatedCisUsersResult.getList())) {
                    log.warn("No related CIS users found for mobile: {}", mobile);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到关联用户信息");
                    return resp;
                }
                // 找customerNo
                String customerNo = relatedCisUsersResult.getList().stream()
                        .filter(e -> StringUtils.isNotBlank(e.getCustNo()))
                        .findAny()
                        .map(UserSearchDTO::getCustNo)
                        .orElse(null);

                log.info("找到的应用用户映射: {}", customerNo);
                // 如果没有找到xyf或xyf01应用下的用户
                if (StringUtils.isBlank(customerNo)) {
                    log.warn("No user found for mobile: {}", mobile);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到customerNo");
                    return resp;
                }

                // 调用服务查询IVR在贷未结清信息
                IVRLoanSettlementDto ivrLoanSettlementDto = vocmngFeignClientImpl.queryIVRLoanSettlement(customerNo, callerMobile);
                if (Objects.nonNull(ivrLoanSettlementDto)) {
                    Map<String, Object> variables = new HashMap<>();
                    // 将数量和金额转换为String类型
                    variables.put("unsettledLoanCount", ivrLoanSettlementDto.getUnsettledLoanCount() != null ? ivrLoanSettlementDto.getUnsettledLoanCount().toString() : "0");
                    variables.put("totalSettlementAmount", ivrLoanSettlementDto.getTotalSettlementAmount() != null ? ivrLoanSettlementDto.getTotalSettlementAmount().toString() : "");
                    variables.put("hasReductionPlan", ivrLoanSettlementDto.getHasReductionPlan() != null ? ivrLoanSettlementDto.getHasReductionPlan().toString() : "false");
                    variables.put("planCount", ivrLoanSettlementDto.getPlanCount() != null ? ivrLoanSettlementDto.getPlanCount().toString() : "0");
                    variables.put("planTotalAmount", ivrLoanSettlementDto.getPlanTotalAmount() != null ? ivrLoanSettlementDto.getPlanTotalAmount().toString() : "");
                    variables.put("earliestPlanDueDate", ivrLoanSettlementDto.getEarliestPlanDueDate() != null ? ivrLoanSettlementDto.getEarliestPlanDueDate().format(DATE_FORMATTER) : "");
                    resp.setVariables(variables);
                    resp.setMessage(new VocKeyResponse.Message());
                } else {
                    log.warn("No IVR loan settlement information found for customerNo: {}", customerNo);
                    resp.setResCode(0);
                    resp.setMessage(new VocKeyResponse.Message());
                    resp.getMessage().setMsgContent("未找到在贷未结清信息");
                }
            } catch (Exception e) {
                log.error("查询IVR在贷未结清信息异常", e);
                resp.setResCode(-1);
                resp.setResError(e.getMessage());
            }
        } else {
            resp.setResCode(-1);
            resp.setResError("缺少必要参数：手机号");
        }
        return resp;
    }

    @Override
    public void sendSms(UdeskSendMessageRequest req) {
        // 验证data中的value不能为空
        if (req.getData() != null) {
            for (Map.Entry<String, Object> entry : req.getData().entrySet()) {
                if (entry.getValue() == null ||
                        (entry.getValue() instanceof String && StringUtils.isBlank((String) entry.getValue()))) {
                    throw new VocprodException(ErrDtlEnum.PARAM_INVALID);
                }
            }
        }
        vocmngFeignClientImpl.sendSms(req);
    }

    @Override
    public VocKeyResponse queryMembershipCards(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        resp.setResCode(0);

        if (Objects.nonNull(req.getCustomerExtra())) {
            try {
//                String callerMobile = req.getCustomerExtra().getCallerNumber();
//                log.info("开始查询手机号关联的会员卡信息, 手机号: {}", callerMobile);
//
//                // 根据手机号查询关联的用户信息
//                PageResult<UserSearchDTO> relatedCisUsersResult = cisFacadeClient.queryUserList(callerMobile, null, null, 1, 10);
//                if (Objects.isNull(relatedCisUsersResult) || CollectionUtils.isEmpty(relatedCisUsersResult.getList())) {
//                    log.warn("No user found for mobile: {}", callerMobile);
//                    resp.setResCode(0);
//                    resp.setMessage(createPlainMessage("未找到用户信息"));
//                    return resp;
//                }

//                UserSearchDTO userSearchDTO = relatedCisUsersResult.getList().get(0);
//                String customerNo = userSearchDTO.getUserNo();
//                log.info("找到用户信息, customerNo: {}", customerNo);

                // 构建会员卡查询的自定义卡片响应
                resp.setMessage(createMembershipCardsMessage("", ""));

            } catch (Exception e) {
                log.error("查询会员卡信息异常", e);
                resp.setResCode(-1);
                resp.setResError(e.getMessage());
            }
        } else {
            resp.setResCode(-1);
            resp.setResError("缺少必要参数：手机号");
        }
        return resp;
    }

    @Override
    public VocKeyResponse queryUserBillDetail(VocKeyRequest req) {
        VocKeyResponse resp = new VocKeyResponse();
        resp.setResCode(0);

        if (Objects.nonNull(req.getCustomerExtra()) && StringUtils.isNotBlank(req.getCustomerExtra().getCallerNumber())) {
            try {
                String callerNumber = req.getCustomerExtra().getCallerNumber();
                log.info("开始查询用户账单详情, 手机号: {}", callerNumber);

                // 从variables中获取账单ID或订单号
                String billId = null;
                String orderNumber = null;
                if (Objects.nonNull(req.getVariables())) {
                    billId = (String) req.getVariables().get("billId");
                    orderNumber = (String) req.getVariables().get("orderNumber");
                }

                // 构建账单详情的自定义卡片响应
                resp.setMessage(createBillDetailMessage(callerNumber, billId, orderNumber));

                // 设置变量信息
                Map<String, Object> variables = new HashMap<>();
                variables.put("totalAmount", "10666.56");
                variables.put("paidAmount", "2666.64");
                variables.put("remainingAmount", "7999.92");
                variables.put("totalPeriods", 12);
                variables.put("paidPeriods", 3);
                variables.put("overduePeriods", 4);
                resp.setVariables(variables);

            } catch (Exception e) {
                log.error("查询账单详情异常", e);
                resp.setResCode(-1);
                resp.setResError(e.getMessage());
            }
        } else {
            resp.setResCode(-1);
            resp.setResError("缺少必要参数：手机号");
        }
        return resp;
    }

    private void whitelistMark(VocKeywordsRequest req) {
        WhitelistMarkRequest w = new WhitelistMarkRequest();
        w.setFunctionScope("RENDER");
        w.setWhiteType("USER");
        w.setWhiteValue(String.valueOf(req.getUserNo()));
        w.setUserNo(String.valueOf(req.getUserNo()));
        w.setCustNo(req.getCustNo());
        w.setRequestId(UUID.fastUUID().toString());
        w.setBizChannel("VOCMNG");
        w.setBizType("REPAYTRADE");
        BizTraceContext b = new BizTraceContext();
        b.setCnlNo(UUID.fastUUID().toString());
        b.setCnlPdCode("CSC005000082");
        b.setCnlEvCode("PY00100001");
        w.setBizTraceContext(b);
        cashiercoreFeignClient.vocKeywordsSet(w);
    }

    private VocKeywordsRequest getReq(Long userNo, String custNo) {
        VocKeywordsRequest r = new VocKeywordsRequest();
        r.setUserNo(userNo);
        r.setCustNo(custNo);
        r.setSysSource(Constants.SERVICE_NAME);
        return r;
    }

    private ImmutableTriple<Boolean, Long, String> validReq(VocKeyRequest req, VocKeyResponse resp) {
        if (Objects.isNull(req) || Objects.isNull(req.getCustomerExtra()) || StringUtils.isBlank(req.getCustomerExtra().getDialogueDesc())) {
            setErrorResponse(resp, -1, "The mandatory parameter is null");
            return ImmutableTriple.of(Boolean.TRUE, null, null);
        }
        Long userNo = parseLong(req.getCustomerExtra().getDialogueDesc());
        if (Objects.isNull(userNo)) {
            setErrorResponse(resp, -1, "userno  is null");
            return ImmutableTriple.of(Boolean.TRUE, null, null);
        }
        return ImmutableTriple.of(Boolean.FALSE, userNo, null);
    }


    private Long parseLong(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        if (obj instanceof Long) {
            return (Long) obj;
        }
        try {
            return Long.parseLong(obj.toString());
        } catch (NumberFormatException e) {
            log.warn("vocKeywordsSet userno is invain,userno:{}", obj.toString());
            return null;
        }
    }

    private void setErrorResponse(VocKeyResponse resp, int code, String error) {
        resp.setResCode(code);
        resp.setResError(error);
    }

    /**
     * 创建普通文本消息
     */
    private VocKeyResponse.Message createPlainMessage(String content) {
        VocKeyResponse.Message message = new VocKeyResponse.Message();
        message.setMsgType("plain");
        message.setMsgContent(content);
        return message;
    }

    /**
     * 创建会员卡查询的自定义卡片消息
     */
    private VocKeyResponse.Message createMembershipCardsMessage(String customerNo, String mobile) {
        VocKeyResponse.Message message = new VocKeyResponse.Message();
        message.setMsgType("custom_card");

        // 模拟会员卡数据（实际应该从数据库或其他服务查询）
        List<MembershipCardDto> membershipCards = getMockMembershipCards(customerNo);

        CustomCardContent cardContent = buildMembershipCardContent(membershipCards);
        message.setMsgContent(cardContent);

        return message;
    }

    /**
     * 构建会员卡自定义卡片内容
     */
    private CustomCardContent buildMembershipCardContent(List<MembershipCardDto> membershipCards) {
        CustomCardContent content = new CustomCardContent();
        content.setId(1);
        content.setName("会员卡查询");
        content.setTurnFlag(0);
        content.setShowSize(membershipCards.size());
        content.setTitle("请选择您要咨询的飞享会员订单");

        List<CustomCardContent.CardItem> cardList = new ArrayList<>();

        for (MembershipCardDto card : membershipCards) {
            CustomCardContent.CardItem cardItem = new CustomCardContent.CardItem();
            cardItem.setId(card.getCardId());
            cardItem.setType(1); // 富文本卡片
            cardItem.setIsHit(1); // 可点击
            cardItem.setHitType(3); // 结构化消息
            cardItem.setHitContent(card.getOrderNumber());

            // 构建卡片的HTML内容
            String cardHtml = buildMembershipCardHtml(card);
            cardItem.setContent(cardHtml);
            cardItem.setHitShowContent(cardHtml);

            cardList.add(cardItem);
        }

        content.setCardList(cardList);
        return content;
    }

    /**
     * 构建会员卡HTML内容
     */
    private String buildMembershipCardHtml(MembershipCardDto card) {
        String statusColor = getStatusColor(card.getStatus());
        String statusText = card.getStatus();

        return String.format(
            "<div style=\"display: flex; margin: 0 20px; padding: 15px 0; border-bottom: 1px solid #E5E5E5;\">" +
            "    <div style=\"flex: 1; display: flex; flex-direction: column; justify-content: center;\">" +
            "        <div style=\"font-size: 16px; font-weight: bold; color: #333; margin-bottom: 8px;\">%s</div>" +
            "        <div style=\"font-size: 12px; color: #999; margin-bottom: 4px;\">有效期：%s至%s</div>" +
            "        <div style=\"font-size: 12px; color: #999;\">下单时间：%s</div>" +
            "    </div>" +
            "    <div style=\"flex: 0 0 auto; display: flex; flex-direction: column; align-items: flex-end; justify-content: center;\">" +
            "        <div style=\"font-size: 18px; font-weight: bold; color: #FF6B35; margin-bottom: 8px;\">实付￥%.2f</div>" +
            "        <div style=\"background-color: %s; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;\">%s</div>" +
            "        <div style=\"margin-top: 8px;\">" +
            "            <button style=\"background-color: #007AFF; color: white; border: none; padding: 6px 16px; border-radius: 4px; font-size: 12px;\">选择</button>" +
            "        </div>" +
            "    </div>" +
            "</div>",
            card.getCardName(),
            card.getValidStartDate().toString(),
            card.getValidEndDate().toString(),
            card.getOrderTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
            card.getActualAmount(),
            statusColor,
            statusText
        );
    }

    /**
     * 根据状态获取颜色
     */
    private String getStatusColor(String status) {
        switch (status) {
            case "状态":
                return "#28A745"; // 绿色
            case "生效中":
                return "#007AFF"; // 蓝色
            case "已结束":
                return "#6C757D"; // 灰色
            default:
                return "#6C757D";
        }
    }

    /**
     * 获取模拟会员卡数据（实际应该从数据库查询）
     */
    private List<MembershipCardDto> getMockMembershipCards(String customerNo) {
        List<MembershipCardDto> cards = new ArrayList<>();

        // 模拟数据1
        MembershipCardDto card1 = new MembershipCardDto();
        card1.setCardId("1");
        card1.setCardName("飞享会员");
        card1.setStatus("状态");
        card1.setActualAmount(new BigDecimal("76.78"));
        card1.setValidStartDate(LocalDate.of(2024, 12, 1));
        card1.setValidEndDate(LocalDate.of(2025, 12, 1));
        card1.setOrderTime(LocalDateTime.of(2024, 12, 1, 17, 22));
        card1.setCardType(1);
        card1.setOrderNumber("VIP202412010001");
        card1.setSelectable(true);
        cards.add(card1);

        // 模拟数据2
        MembershipCardDto card2 = new MembershipCardDto();
        card2.setCardId("2");
        card2.setCardName("飞享会员");
        card2.setStatus("生效中");
        card2.setActualAmount(new BigDecimal("76.78"));
        card2.setValidStartDate(LocalDate.of(2024, 12, 1));
        card2.setValidEndDate(LocalDate.of(2025, 12, 1));
        card2.setOrderTime(LocalDateTime.of(2024, 12, 1, 17, 22));
        card2.setCardType(1);
        card2.setOrderNumber("VIP202412010002");
        card2.setSelectable(true);
        cards.add(card2);

        // 模拟数据3
        MembershipCardDto card3 = new MembershipCardDto();
        card3.setCardId("3");
        card3.setCardName("飞享会员");
        card3.setStatus("已结束");
        card3.setActualAmount(new BigDecimal("76.78"));
        card3.setValidStartDate(LocalDate.of(2024, 12, 1));
        card3.setValidEndDate(LocalDate.of(2025, 12, 1));
        card3.setOrderTime(LocalDateTime.of(2024, 12, 1, 17, 22));
        card3.setCardType(1);
        card3.setOrderNumber("VIP202412010003");
        card3.setSelectable(true);
        cards.add(card3);

        return cards;
    }

    /**
     * 创建账单详情查询的自定义卡片消息
     */
    private VocKeyResponse.Message createBillDetailMessage(String mobile, String billId, String orderNumber) {
        // 模拟账单详情数据（实际应该从数据库或其他服务查询）
        List<BillDetailDto> billDetails = getMockBillDetails(mobile, billId, orderNumber);

        // 使用模板系统构建卡片
        CustomCardBuilder.CardConfig config = new CustomCardBuilder.CardConfig();
        config.setId(2);
        config.setName("账单详情");
        config.setShowSize(billDetails.size());
        config.setShowTimestamp(true);
        config.setSpeakerName("小飞");
        config.setSubTitle("xxx引导语");
        config.setCardId("bill_timeline");
        config.setClickable(false);
        config.setUseTemplate(true);
        config.setTemplateType(HtmlTemplateManager.TemplateType.BILL_TIMELINE);
        config.setData(billDetails);

        // 设置模板配置参数
        Map<String, Object> templateConfig = new HashMap<>();
        templateConfig.put("backgroundColor", "#f8f9fa");
        templateConfig.put("padding", "10px 20px");
        templateConfig.put("showTimeline", true);
        config.setTemplateConfig(templateConfig);

        return customCardBuilder.buildCustomCardMessage(config);
    }

    /**
     * 构建账单详情自定义卡片内容
     */
    private CustomCardContent buildBillDetailContent(List<BillDetailDto> billDetails) {
        CustomCardContent content = new CustomCardContent();
        content.setId(2);
        content.setName("账单详情");
        content.setTurnFlag(0);
        content.setShowSize(billDetails.size());

        // 添加时间戳标题
        LocalDateTime now = LocalDateTime.now();
        String timeStamp = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        content.setTitle("小飞    " + timeStamp);
        content.setSubTitle("xxx引导语");

        List<CustomCardContent.CardItem> cardList = new ArrayList<>();

        // 创建一个单独的卡片包含所有账单项
        CustomCardContent.CardItem cardItem = new CustomCardContent.CardItem();
        cardItem.setId("bill_timeline");
        cardItem.setType(1); // 富文本卡片
        cardItem.setIsHit(0); // 不可点击
        cardItem.setHitType(null); // 不设置点击效果
        cardItem.setHitContent(null);
        cardItem.setHitShowContent(null);

        // 构建完整的时间轴HTML内容
        String timelineHtml = buildBillTimelineHtml(billDetails);
        cardItem.setContent(timelineHtml);

        cardList.add(cardItem);
        content.setCardList(cardList);
        return content;
    }

    /**
     * 构建账单时间轴HTML内容
     */
    private String buildBillTimelineHtml(List<BillDetailDto> billDetails) {
        StringBuilder html = new StringBuilder();
        html.append("<div style='padding: 10px 20px; background-color: #f8f9fa;'>");

        for (int i = 0; i < billDetails.size(); i++) {
            BillDetailDto bill = billDetails.get(i);
            String statusColor = getBillStatusColor(bill.getStatus());
            boolean isLast = (i == billDetails.size() - 1);

            html.append(String.format(
                "<div style='display: flex; align-items: center; padding: 8px 0; position: relative;'>" +
                "    <div style='flex: 0 0 50px; text-align: left; margin-right: 20px; position: relative;'>" +
                "        <div style='font-size: 14px; color: #333; margin-bottom: 2px;'>%s</div>" +
                "        <div style='font-size: 12px; color: #999;'>%s</div>" +
                "        <div style='position: absolute; right: -10px; top: 50%%; transform: translateY(-50%%); width: 8px; height: 8px; border-radius: 50%%; background-color: #E5E5E5; border: 2px solid #fff; z-index: 2;'></div>" +
                (isLast ? "" : "        <div style='position: absolute; right: -6px; top: 50%%; width: 2px; height: 40px; background-color: #E5E5E5; z-index: 1;'></div>") +
                "    </div>" +
                "    <div style='flex: 1; margin-left: 10px;'>" +
                "        <span style='font-size: 16px; font-weight: bold; color: #333; margin-right: 20px;'>¥%.2f</span>" +
                "        <span style='font-size: 14px; color: %s;'>%s</span>" +
                "    </div>" +
                "</div>",
                bill.getPeriodName(),
                bill.getRepaymentDate().format(DateTimeFormatter.ofPattern("MM/dd")),
                bill.getAmount(),
                statusColor,
                bill.getStatus()
            ));
        }

        html.append("</div>");
        return html.toString();
    }


    /**
     * 根据账单状态获取颜色
     */
    private String getBillStatusColor(String status) {
        switch (status) {
            case "已还":
                return "#28A745"; // 绿色
            case "逾期":
                return "#DC3545"; // 红色
            case "未到期":
                return "#999999"; // 灰色
            default:
                return "#999999";
        }
    }

    /**
     * 获取模拟账单详情数据（实际应该从数据库查询）
     */
    private List<BillDetailDto> getMockBillDetails(String mobile, String billId, String orderNumber) {
        List<BillDetailDto> bills = new ArrayList<>();

        // 模拟12期账单数据
        String[] periodNames = {"首期", "2期", "3期", "4期", "5期", "6期", "7期", "8期", "9期", "10期", "11期", "12期"};
        String[] statuses = {"已还", "已还", "已还", "逾期", "逾期", "逾期", "逾期", "未到期", "未到期", "未到期", "未到期", "未到期"};
        String[] dates = {"12/01", "01/01", "02/01", "02/01", "01/01", "02/01", "02/01", "01/01", "02/01", "02/01", "02/01", "02/01"};

        for (int i = 0; i < 12; i++) {
            BillDetailDto bill = new BillDetailDto();
            bill.setPeriodId(String.valueOf(i + 1));
            bill.setPeriodName(periodNames[i]);
            bill.setRepaymentDate(LocalDate.of(2024, (i % 2) + 1, 1));
            bill.setAmount(new BigDecimal("888.88"));
            bill.setStatus(statuses[i]);
            bill.setOrderNumber("ORDER" + System.currentTimeMillis() + i);
            bill.setClickable(true);

            if ("逾期".equals(statuses[i])) {
                bill.setOverdueDays(i * 5);
                bill.setOverdueFee(new BigDecimal("50.00"));
            }

            bills.add(bill);
        }

        return bills;
    }

}