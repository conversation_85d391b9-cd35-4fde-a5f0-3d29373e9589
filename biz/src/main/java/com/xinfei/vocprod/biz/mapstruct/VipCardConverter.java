/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.mapstruct;

import com.xinfei.supervip.common.enums.RefundAccountTypeEnum;
import com.xinfei.supervip.common.enums.RefundStatusEnum;
import com.xinfei.supervip.interfaces.model.admin.dto.RefundDetailAdminDTO;
import com.xinfei.vipcore.facade.rr.dto.RefundApplyLastResDto;
import com.xinfei.vipcore.facade.rr.dto.RefundDto;
import com.xinfei.vocprod.biz.api.rr.RefundApplyLastRes;
import com.xinfei.vocprod.biz.api.rr.RefundLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ CustomerConverter, v 0.1 2023-12-20 19:57 junjie.yan Exp $
 */
@Mapper
public interface VipCardConverter {

    VipCardConverter INSTANCE = Mappers.getMapper(VipCardConverter.class);

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    default LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

    default LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }

        return date.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
    }

    default String localDateTimeToString(LocalDateTime date) {
        if (date == null) {
            return "";
        }
        return date.format(formatter);
    }

    @Mapping(source = "status", target = "status", qualifiedByName = "getStatus")
    RefundLog refundDtoToRefundLog(RefundDto refundDto);

    @Mapping(source = "refundStatus", target = "status", qualifiedByName = "getAdminStatus")
    @Mapping(source = "vipCardName", target = "cardName")
    @Mapping(source = "refundAmount", target = "amount")
    @Mapping(source = "refundAccountType", target = "refundAccountType", qualifiedByName = "getRefundAccountType")
    RefundLog refundDetailAdminDtoToRefundLog(RefundDetailAdminDTO refundDto);

    RefundApplyLastRes resDtoDtoToRefundApplyLastRes(RefundApplyLastResDto refundDto);

    @Named("getStatus")
    static Integer getStatus(Integer status) {
        if (status == 2) {
            return 0;
        } else {
            return status;
        }
    }

    @Named("getAdminStatus")
    static Integer getAdminStatus(String refundStatus) {
        if (RefundStatusEnum.REFUND_SUCCESS.getCode().equals(refundStatus)) {
            return 1;
        } else {
            return 0;
        }
    }

    @Named("getRefundAccountType")
    static Integer getRefundAccountType(String refundAccountType) {
        if (RefundAccountTypeEnum.ALIPAY.getCode().equals(refundAccountType)) {
            return 2;
        } else if (RefundAccountTypeEnum.DEBIT_CARD.getCode().equals(refundAccountType)){
            return 1;
        }
        return 1;
    }


}