/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api.rr;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version $ BillingSubmitRequest, v 0.1 2024-11-14 15:53 junjie.yan Exp $
 */
@Data
public class BillingSubmitRequest {
    @ApiModelProperty("姓名")
//    @NotBlank(message = "name不能为空")
//    @Size(min = 1, max = 10, message = "姓名不能超过10个字")
    private String name;

    @ApiModelProperty("手机号")
//    @NotBlank(message = "mobile不能为空")
//    @Pattern(regexp = "/^0?(13|14|15|16|17|18|19)[0-9]{9}$/", message = "手机号格式有误")
    private String mobile;

    @ApiModelProperty("身份证号")
    @JsonProperty("id_card_number")
//    @NotBlank(message = "id_card_number不能为空")
//    @Pattern(regexp = "/^\\d{17}[\\d|x|X]$/", message = "身份证号格式错误")
    private String idCardNumber;

    @ApiModelProperty("邮箱")
//    @NotBlank(message = "email不能为空")
    private String email;

    @ApiModelProperty("具体诉求")
//    @NotBlank(message = "appeal不能为空")
//    @Size(min = 1, max = 1000, message = "具体诉求不能超过1000个字")
    private String appeal;
}