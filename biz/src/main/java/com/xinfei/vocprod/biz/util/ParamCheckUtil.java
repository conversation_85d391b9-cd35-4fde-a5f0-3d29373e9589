/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.util;

import com.xinfei.vocprod.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocprod.biz.model.exception.TechplayException;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 参数校验工具类
 *
 * <AUTHOR>
 * @version $ ParamCheckUtil, v 0.1 2023/8/28 18:35 Jinyan.Huang Exp $
 */
public final class ParamCheckUtil {

    /**
     * 团队代码
     */
    public static final String TEAM_CODE = "团队代码:teamCode";

    /**
     * 请求参数
     */
    public static final String USER_REQUEST = "请求参数:request";

    /**
     * 用户
     */
    public static final String USER_REQUEST_USER = "用户:user";

    /**
     * 用户名称
     */
    public static final String USER_NAME = "用户名称:name";

    /**
     * 用户email
     */
    public static final String USER_EMAIL = "用户email:email";

    /**
     * 应用名
     */
    public static final String APP_NAME = "应用名:appName";

    /**
     * 私有构造函数
     */
    private ParamCheckUtil() {
        // 私有构造函数
    }

    /**
     * 检查请求参数字符串是否空字符串
     *
     * @param paramValue 请求字符串
     * @param paramName  参数名称
     */
    public static void checkParamNotBlank(String paramValue, String paramName) {
        if (!StringUtils.hasText(paramValue)) {
            String msg = paramName + "不允许为空";
            throw new TechplayException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, msg);
        }
    }

    /**
     * 检查请求列表是否空
     *
     * @param collection 请求列表
     * @param paramName  参数名称
     */
    public static void checkParamNotEmpty(List<?> collection, String paramName) {
        if (CollectionUtils.isEmpty(collection)) {
            String msg = paramName + "不允许为空";
            throw new TechplayException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, msg);
        }
    }

    /**
     * 检查请求对象是否为空
     *
     * @param para     请求参数
     * @param paraName 参数名称
     */
    public static void checkParamNotNull(Object para, String paraName) {
        if (para == null) {
            String msg = paraName + "不允许为null";
            throw new TechplayException(TechplayErrDtlEnum.REQ_PARAM_NOT_VALID, msg);
        }
    }
}
