/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.service.impl;

import apollo.com.google.gson.reflect.TypeToken;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.xinfei.cisaggs.facade.rr.StandardBatchCustomerInfoResponse;
import com.xinfei.cisaggs.facade.rr.StandardCustomerInfoResponse;
import com.xinfei.cisaggs.facade.rr.dto.CustBasicInfoDTO;
import com.xinfei.cisaggs.facade.rr.dto.FamilyEduDTO;
import com.xinfei.cisaggs.facade.rr.dto.JobDTO;
import com.xinfei.vipcore.facade.rr.dto.UserVipStatusDto;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskLinkRequest;
import com.xinfei.vocprod.biz.api.rr.HelpCenterGetUdeskResultDto;
import com.xinfei.vocprod.biz.common.RandomBizKey;
import com.xinfei.vocprod.biz.config.AppVocConfig;
import com.xinfei.vocprod.biz.enums.AppErrDtlEnum;
import com.xinfei.vocprod.biz.enums.AppException;
import com.xinfei.vocprod.biz.model.entity.RequestUser;
import com.xinfei.vocprod.biz.model.entity.UdeskConfBo;
import com.xinfei.vocprod.biz.service.IndexService;
import com.xinfei.vocprod.biz.service.UDeskService;
import com.xinfei.vocprod.biz.service.VipService;
import com.xinfei.vocprod.biz.service.common.CisCommonService;
import com.xinfei.vocprod.biz.util.IDCardUtil;
import com.xinfei.vocprod.facade.rr.UDeskRequest;
import com.xinfei.vocprod.facade.rr.dto.Customer;
import com.xinfei.vocprod.itl.exception.ClientException;
import com.xinfei.vocprod.itl.exception.ErrorLevelsEnum;
import com.xinfei.vocprod.itl.exception.FeignErrDtlEnum;
import com.xinfei.vocprod.itl.impl.*;
import com.xinfei.vocprod.itl.rr.AmsAccountInfo;
import com.xinfei.vocprod.itl.rr.MlMetadata;
import com.xinfei.vocprod.itl.rr.ProblemEvaluation;
import com.xinfei.vocprod.util.LogUtil;
import com.xinfei.vocprod.util.enums.MemberTypeEnum;
import com.xinfei.xfframework.common.JsonUtil;
import com.xyf.cis.query.facade.dto.standard.response.LastLoginDTO;
import com.xyf.cis.query.facade.dto.standard.response.UserSearchDTO;
import com.xyf.ext.info.model.Challenge;
import com.xyf.user.facade.common.model.PageResult;
import io.kyoto.pillar.lcs.loan.domain.request.LoanPlanRequest;
import io.kyoto.pillar.lcs.loan.domain.response.LoanPlanResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $ UDeskServiceImpl, v 0.1 2024/7/8 14:10 wancheng.qu Exp $
 */
@Service
@Slf4j
public class IndexServiceImpl implements IndexService {

    @Resource
    private VipService vipService;

    @Resource
    private CisCommonService cisCommonService;

    @Resource
    private CisFacadeClientImpl cisFacadeClient;

    @Resource
    private FeatureQueryClientImpl featureQueryClient;

    @Resource
    private LcsFeignService lcsFeignService;

    @Resource
    private UDeskService uDeskService;

    @Resource
    private AppVocConfig appVocConfig;

    @Resource
    private VipFacadeClientImpl vipFacadeClient;

    @Resource
    private RandomGeneratorClientImpl randomGeneratorClient;

    @Value("${client.vip.udesk-config}")
    private String udeskVipConfig;

    @Resource(name = "asyncExecutor")
    private Executor executorService;

    /**
     * xyf下线开关
     */
    @Value("${isStandardBatch:false}")
    private Boolean isStandardBatch;

    @Resource
    private CreditFeignService creditFeignService;

    @Resource
    private WorkOrderFeignClientImpl workOrderFeignClient;

    @Resource
    private VocmngFeignClientImpl vocmngFeignClient;

    @Resource
    private DmxFeignClientImpl dmxFeignClient;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private final Gson gson = new Gson();
    private final Type type = new TypeToken<Map<String, Object>>() {
    }.getType();

    private final Type typeList = new TypeToken<List<Map<String, Object>>>() {
    }.getType();

    @Override
    public Map<String, Object> config(String userNo) {

        // 创建一个新的 Map 来存储解析后的 JSON 数据，默认非会员无入口
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("app_udesk", gson.fromJson(appVocConfig.getAppUDesk(), type));
        resultMap.put("header", gson.fromJson(appVocConfig.getHeader(), type));
        resultMap.put("abtest", gson.fromJson(appVocConfig.getAbtest(), type));
        resultMap.put("navigate", gson.fromJson(appVocConfig.getNavigate(), typeList));
        resultMap.put("bottom", gson.fromJson(appVocConfig.getBottom(), type));
        resultMap.put("question_category", gson.fromJson(appVocConfig.getQuestionCategory(), typeList));

        boolean isGroupB = "groupB".equals(randomGeneratorClient.ab(userNo, RandomBizKey.KFDT_HYJZX));

        if (isGroupB) {
            if (vipService.isVipNew(userNo)) {
                //飞享会员
                //会员有入口
                if (vipService.showVipEntrance(userNo)) {
                    resultMap.put("navigate", gson.fromJson(appVocConfig.getVipNavigate(), typeList));
                } else {
                    //会员无入口
                    resultMap.put("navigate", gson.fromJson(appVocConfig.getVipNoEntranceNavigate(), typeList));
                }
            } else {
                //会员有入口
                if (vipService.showVipEntrance(userNo)) {
                    resultMap.put("navigate", gson.fromJson(appVocConfig.getNotVipNavigate(), typeList));
                }
            }
        }

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> navigateList = (List<Map<String, Object>>) resultMap.get("navigate");
        for (Map<String, Object> item : navigateList) {
            if ("会员问题".equals(item.get("title"))) {
                String group = "groupA";
                try {
                    group = randomGeneratorClient.ab(userNo, RandomBizKey.KFDT_HYWTREKYD);
                } catch (Exception e) {
                    log.error("randomGeneratorClient.ab error", e);
                }
                item.put("red", group);
                break;
            }
        }

        return resultMap;
    }

    @Override
    public Map<String, Object> faqConfig(String userNo) {
        boolean isGroupB = "groupB".equals(randomGeneratorClient.ab(userNo, RandomBizKey.KFDT_CNXW_DMX));
        //默认的猜你想问FAQ
        Map<String, Object> defaultConfig = gson.fromJson(appVocConfig.getQuestions1(), type);
        defaultConfig.put("isDmx", false);
        List<Long> asyncResult = null; // 大模型默认值初始化

        List<Map<String, Object>> questions = new ArrayList<>();

        if (isGroupB) {
            // --- 异步任务开始 ---
            // 1. 定义异步任务（示例：获取动态配置）
            // 需要异步执行的业务逻辑（如远程调用/复杂计算）
            log.info(LogUtil.infoLog("开始执行大模型任务", userNo));
            Callable<List<Long>> task = () -> this.getDynamicConfig(userNo);

            // 2. 提交任务并设置超时
            Future<List<Long>> future = ((ThreadPoolTaskExecutor) executorService).submit(task);

            try {
                asyncResult = future.get(appVocConfig.getFoundationalModelTimeout(), TimeUnit.MILLISECONDS); // 设置超时时间为3秒
                log.info(LogUtil.infoLog("大模型任务执行正常", userNo));
            } catch (TimeoutException e) {
                log.warn(LogUtil.infoLog("大模型任务超时，使用默认值处理", userNo));
            } catch (InterruptedException | ExecutionException e) {
                log.error(LogUtil.infoLog("大模型任务执行异常", userNo, e));
                Thread.currentThread().interrupt(); // 恢复线程中断状态
            }
        }

        // 4. 解析defaultConfig中的list
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> list = (List<Map<String, Object>>) defaultConfig.get("list");
        // 5. 根据asyncResult的ID顺序对list进行排序
        if (CollectionUtils.isNotEmpty(asyncResult)) {
            // 创建一个映射来存储每个元素的原始索引
            Map<Long, Map<String, Object>> idToItemMap = new LinkedHashMap<>();
            for (Map<String, Object> item : list) {
                Long id = ((Number) item.get("id")).longValue();
                idToItemMap.put(id, item);
            }

            // 根据asyncResult的顺序构建新的list
            List<Map<String, Object>> sortedList = new ArrayList<>();
            for (Long id : asyncResult) {
                if (idToItemMap.containsKey(id)) {
                    Map<String, Object> item = idToItemMap.get(id);
                    item.put("can_see_user_type", "all"); // 设置字段值
                    sortedList.add(item);
                }
            }

            // 更新defaultConfig中的list
            defaultConfig.put("list", sortedList);
            defaultConfig.put("isDmx", true);
        }

        // 在调用 Feign 之前，检查并清除中断状态
        if (Thread.interrupted()) { // Thread.interrupted() 会检查并清除中断状态
            log.warn(LogUtil.infoLog("当前线程曾被中断，现已清除中断状态以继续执行后续Feign调用", userNo));
        }

        //所有FAQ
        //猜你想问
        questions.add(defaultConfig);
        if (vipService.showRefundEntry(userNo)) {
            //提额卡
            questions.add(gson.fromJson(appVocConfig.getQuestions7(), type));
        }
        if (vipService.isSuperVip(userNo) || MemberTypeEnum.FEI_YUE.getCode().equals(vipService.vipClassifyType(userNo))) {
            //飞跃会员权益
            questions.add(gson.fromJson(appVocConfig.getQuestions8(), type));
        } else {
            //权益+飞享会员权益
            questions.add(gson.fromJson(appVocConfig.getQuestions2(), type));
        }


        //还款
        questions.add(gson.fromJson(appVocConfig.getQuestions4(), type));
        //结清
        questions.add(gson.fromJson(appVocConfig.getQuestions6(), type));
        //费用
        questions.add(gson.fromJson(appVocConfig.getQuestions9(), type));
        //逾期
        questions.add(gson.fromJson(appVocConfig.getQuestions10(), type));
        //账户
        questions.add(gson.fromJson(appVocConfig.getQuestions5(), type));
        //借款
        questions.add(gson.fromJson(appVocConfig.getQuestions3(), type));
        //其他
        questions.add(gson.fromJson(appVocConfig.getQuestions11(), type));

        if (MemberTypeEnum.FEI_XIANG.getCode().equals(vipService.vipType(userNo))) {
            //飞享会员操作问题
            questions.add(gson.fromJson(appVocConfig.getVipOperationQuestions(), type));
        } else if (MemberTypeEnum.FEI_YUE.getCode().equals(vipService.vipType(userNo))) {
            //飞跃会员操作问题
            questions.add(gson.fromJson(appVocConfig.getVipOperationFeiYueQuestions(), type));
        }

        // 创建一个新的 Map 来存储解析后的 JSON 数据，默认非会员无入口
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("questions", questions);

        return resultMap;
    }

    public List<Long> getDynamicConfig(String userNo) {
        MlMetadata metadata = generalMlMetadata(userNo);
        List<ProblemEvaluation> problemEvaluations = dmxFeignClient.queryGuess(metadata);
        if (CollectionUtils.isNotEmpty(problemEvaluations)) {
            return problemEvaluations.stream()
                    .map(ProblemEvaluation::getQuestionId)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private MlMetadata generalMlMetadata(String userNoStr) {
        Long userNoLong;
        try {
            userNoLong = Long.parseLong(userNoStr);
        } catch (NumberFormatException e) {
            log.warn("无效的用户编号格式: {}", userNoStr, e);
            throw new ClientException(FeignErrDtlEnum.PARAS_ERROR, "无效的用户编号格式: " + userNoStr, ErrorLevelsEnum.ERROR);
        }

        MlMetadata metadata = new MlMetadata();
        metadata.setUserNo(userNoLong);
        metadata.setStatus("10");

        // 最后登录时间
        LastLoginDTO lastLoginDTO = cisFacadeClient.queryLastLoginByUserNo(userNoLong);
        if (ObjectUtils.isEmpty(lastLoginDTO)) {
            throw new AppException(AppErrDtlEnum.USER_NOT_EXIST);
        }
        metadata.setLastLoginTime(sdf.format(lastLoginDTO.getLastLoginTime()));

        // 用户年龄
        // 注册时间
        PageResult<UserSearchDTO> pageResult = cisFacadeClient.queryUserList(null, null, userNoStr, 1, 10);
        UserSearchDTO userSearchDTO = pageResult.getList().get(0);
        metadata.setAge(IDCardUtil.getAge(userSearchDTO.getIdNo()));
        metadata.setRegisterTime(sdf.format(userSearchDTO.getRegisterTime()));

        if (StringUtils.isNotBlank(userSearchDTO.getCustNo()) && StringUtils.isNotBlank(userSearchDTO.getApp())) {
            if (!isStandardBatch) {
                Challenge challenge = cisFacadeClient.queryLatestUserExtInfoData(userSearchDTO.getCustNo(), userSearchDTO.getApp());
                if (challenge != null) {
                    //收入水平
                    metadata.setIncome(challenge.getJob().getIncome());
                    //工作单位
                    metadata.setJobGrade(challenge.getJob().getJobGrade());
                    //学历
                    metadata.setEducation(challenge.getFamilyEdu().getEducation());
                    //婚姻状况
                    metadata.setMarriageStatus(challenge.getFamilyEdu().getMarriageStatus());
                }
            } else {
                StandardBatchCustomerInfoResponse standardBatchCustomerInfoResponse = cisFacadeClient.queryStandardBatchCustomerInfo(userSearchDTO.getCustNo(), userSearchDTO.getApp());
                if (standardBatchCustomerInfoResponse != null && CollectionUtils.isNotEmpty(standardBatchCustomerInfoResponse.getResponseList())) {
                    StandardCustomerInfoResponse standardCustomerInfoResponse = standardBatchCustomerInfoResponse
                            .getResponseList()
                            .stream()
                            .filter(r -> r.getApp().equals(userSearchDTO.getApp()))
                            .findFirst()
                            .orElse(null);
                    if (standardCustomerInfoResponse != null) {
                        CustBasicInfoDTO custBasicInfoDTO = standardCustomerInfoResponse.getCustBaseInfo();
                        if (custBasicInfoDTO != null) {
                            FamilyEduDTO familyEduDTO = custBasicInfoDTO.getFamilyEdu();
                            if (familyEduDTO != null) {
                                //学历
                                metadata.setEducation(familyEduDTO.getEducation());
                                //婚姻状况
                                metadata.setMarriageStatus(familyEduDTO.getMarriageStatus());
                            }

                            JobDTO jobDTO = custBasicInfoDTO.getJob();
                            if (jobDTO != null) {
                                //收入水平
                                metadata.setIncome(jobDTO.getIncome());
                                //工作单位
                                metadata.setJobGrade(jobDTO.getJobGrade());
                            }
                        }
                    }
                }
            }


            LoanPlanRequest loanPlanRequest = new LoanPlanRequest();
            loanPlanRequest.setCustNos(Collections.singletonList(userSearchDTO.getCustNo()));
            List<LoanPlanResponse> loanPlanResponses = lcsFeignService.planDetail(loanPlanRequest);
            loanPlanResponses = loanPlanResponses.stream().filter(r -> r.getIsProfitLoan() == 1).collect(Collectors.toList());
            loanPlanResponses = loanPlanResponses.stream().filter(r -> userNoStr.equals(r.getUserNo())).collect(Collectors.toList());

            // 现金订单借款成功
            metadata.setSucLoanOrdersNum(loanPlanResponses.size());
            // 逾期笔数
            metadata.setOverdueOrdersNum((int) loanPlanResponses.stream().filter(r -> "OD".equals(r.getStatus())).count());
            // 还款笔数
            metadata.setNormalOrdersNum((int) loanPlanResponses.stream().filter(r -> "FP".equals(r.getStatus())).count());

            AmsAccountInfo cashAccountInfo = creditFeignService.amountInfo(userSearchDTO.getCustNo(), userSearchDTO.getApp());
            // 信用现金额度
            metadata.setCashCreditAmt(cashAccountInfo.getCreditAmt() == null ? null : cashAccountInfo.getCreditAmt().multiply(new BigDecimal("0.01")).floatValue());
            // 借记现金额度
            metadata.setCashDebitAmt(cashAccountInfo.getDebitAmt() == null ? null : cashAccountInfo.getDebitAmt().multiply(new BigDecimal("0.01")).floatValue());
        }

        //是否疑似黑产
        String reason = featureQueryClient.getCode(userSearchDTO.getMobile(), "phone_is_black_industry_hlevel_type");
        if (StringUtils.isNotBlank(reason) && !reason.equals("n")) {
            metadata.setSuspectFraud(true);
        } else {
            metadata.setSuspectFraud(false);
        }

        //是否飞享会员
        metadata.setIsVip(vipService.isVip(userNoStr));

        // 飞享会员续期状态
        UserVipStatusDto vipStatusDto = vipFacadeClient.userVipStatus(userNoLong);
        if (vipStatusDto != null) {
            metadata.setRenewStatus(vipStatusDto.getRenewStatus());
        }

        // 工单数
        metadata.setTaskQuantity(workOrderFeignClient.getTaskByUserNo(userNoLong).getTotal());
        //近7天小结数
        metadata.setSummaryLogs(vocmngFeignClient.summaryLogs(userNoLong));

        return metadata;
    }

    @Override
    public HelpCenterGetUdeskResultDto getUdeskLink(HelpCenterGetUdeskLinkRequest request) {
        RequestUser requestUser = cisCommonService.getRequestUserByUserNo(Long.parseLong(request.getUserNo()));
        if (ObjectUtils.isEmpty(requestUser)) {
            throw new AppException(AppErrDtlEnum.USER_NOT_EXIST);
        }
        String url = getUdeskLinkFromProd(requestUser, request.getApp());
        if (ObjectUtils.isEmpty(url)) {
            throw new AppException(AppErrDtlEnum.UDESK_GET_FIELD);
        }
        HelpCenterGetUdeskResultDto helpCenterGetUdeskResultDto = new HelpCenterGetUdeskResultDto();
        helpCenterGetUdeskResultDto.setUrl(url);
        return helpCenterGetUdeskResultDto;
    }

    public String getUdeskLinkFromProd(RequestUser requestUser, String app) {
        UDeskRequest uDeskRequest = initUdeskRequest(requestUser.getUserNo().toString(), app);
        if (ObjectUtils.isEmpty(uDeskRequest)) {
            return null;
        }
        return uDeskService.getRsaUrl(uDeskRequest);
    }

    private UDeskRequest initUdeskRequest(String userNo, String app) {
        if (ObjectUtils.isEmpty(app)) {
            return null;
        }
        UdeskConfBo udeskConf = getUdeskConf(app);
        if (ObjectUtils.isEmpty(udeskConf)) {
            return null;
        }
        UDeskRequest uDeskRequest = new UDeskRequest();
        Customer customer = new Customer();
        customer.setWeb_token(userNo);
        customer.setC_cf_uid(userNo);
        customer.setC_cf_dialogueDesc(userNo);
//        uDeskRequest.setTraceId(AppRequestUtil.getRequestId());
        uDeskRequest.setUrlBase(udeskConf.getUrl());
        uDeskRequest.setUpbKey(udeskConf.getPublicKey());
        uDeskRequest.setImUserKey(udeskConf.getImUserKey());
        uDeskRequest.setCustomer(customer);
        return uDeskRequest;
    }

    private UdeskConfBo getUdeskConf(String app) {
        try {
            Map<String, UdeskConfBo> confBoMap;
            confBoMap = JsonUtil.parseJson(udeskVipConfig, new TypeReference<Map<String, UdeskConfBo>>() {
            });

            if (ObjectUtils.isEmpty(confBoMap) || !confBoMap.containsKey(app)) {
                return null;
            }
            return confBoMap.get(app);
        } catch (Exception e) {
            log.info(LogUtil.clientLog("getUdeskConf-error", new HashMap<String, Object>() {{
                put("app", app);
                put("err", e.getMessage());
            }}));
        }
        return null;
    }
}