/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.cache.enums;

import com.xinfei.vocprod.biz.cache.SysparamLocalCache;
import lombok.Getter;

/**
 * 缓存名称枚举
 *
 * <AUTHOR>
 * @version $ LocalCacheNameEnum, v 0.1 2023/10/7 20:25 Jinyan.huang Exp $
 */
@Getter
public enum LocalCacheNameEnum {

    /**
     * 系统参数
     */
    SYSTEM_PARAM(SysparamLocalCache.CACHE_NAME, "系统参数"),

    ;

    /**
     * 枚举代码
     */
    private final String code;

    /**
     * 枚举描述
     */
    private final String description;

    /**
     * <b>私有</b>构造方法
     *
     * @param code        枚举代码
     * @param description 枚举描述
     */
    private LocalCacheNameEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码值获取枚举对象
     *
     * @param code 根据枚举的代码获取枚举对象
     * @return 返回枚举对象，如果没有对应枚举对象时返回<code>null</code>
     */
    public static LocalCacheNameEnum getByCode(String code) {
        for (LocalCacheNameEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

}
