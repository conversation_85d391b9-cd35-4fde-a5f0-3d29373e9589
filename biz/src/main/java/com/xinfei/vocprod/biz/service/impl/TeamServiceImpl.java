/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.service.impl;

import com.xinfei.vocprod.biz.service.TeamService;
import com.xinfei.vocprod.facade.rr.dto.TeamDto;
import org.springframework.stereotype.Service;

/**
 * 团队服务接口默认实现
 *
 * <AUTHOR>
 * @version $ TeamServiceImpl, v 0.1 2023/8/29 09:02 <PERSON>yan.Huang Exp $
 */
@Service
public class TeamServiceImpl implements TeamService {

    @Override
    public TeamDto query(String teamCode) {

        TeamDto teamDto = new TeamDto();

        teamDto.setTeamCode(teamCode);
        teamDto.setTeamName("信飞技术中心");

        return teamDto;
    }
}
