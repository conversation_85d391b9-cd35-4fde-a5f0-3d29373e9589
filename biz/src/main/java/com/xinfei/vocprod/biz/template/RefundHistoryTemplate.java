/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import java.util.Map;

/**
 * 退款历史HTML模板
 *
 * <AUTHOR>
 * @version $ RefundHistoryTemplate, v 0.1 2025/1/2 14:35 shaohui.chen Exp $
 */
public class RefundHistoryTemplate implements HtmlTemplate {

    @Override
    public String render(Object data, Map<String, Object> config) {
        // TODO: 实现退款历史模板
        return "<div>退款历史模板待实现</div>";
    }

    @Override
    public String getTemplateName() {
        return "退款历史";
    }

    @Override
    public Class<?> getSupportedDataType() {
        return Object.class;
    }
}
