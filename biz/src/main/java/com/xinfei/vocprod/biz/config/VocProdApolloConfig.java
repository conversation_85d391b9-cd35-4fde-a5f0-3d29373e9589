/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $ ApolloConfig, v 0.1 2024/8/30 14:10 wancheng.qu Exp $
 */
@Getter
@Component
@RefreshScope
public class VocProdApolloConfig {

    @Value("${cash.open.flag:true}")
    private boolean cashOpenFlag;

    @Value("${enc.ccf.uid:true}")
    private boolean encfUid;

    @Value("${cust.new.enabled:false}")
    private boolean custNewEnabled;

    @Value("${super.vip.switch:false}")
    private boolean superVipSwitch;
}