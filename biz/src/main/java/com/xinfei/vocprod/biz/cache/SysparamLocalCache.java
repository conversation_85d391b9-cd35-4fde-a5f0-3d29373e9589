/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.cache;

import com.xinfei.xfframework.common.starter.localcache.LocalCache;

/**
 * 系统参数缓存
 *
 * <AUTHOR>
 * @version $ SysparamLocalCache, v 0.1 2023/10/8 20:44 Jinyan.huang Exp $
 */
public interface SysparamLocalCache extends LocalCache {

    public static final String CACHE_NAME="SYSTEM_PARAM";

    /**
     * 根据参数代码获取参数值
     *
     * @param sysCode 系统参考代码
     * @return 系统参数值
     */
    String fetchSystemValue(String sysCode);

    /**
     * 是否位切流白名单账号
     *
     * @param accountNo 账号
     * @return 如果账号在白名单列表返回<code>true</code>，否则返回<code>false</code>
     */
    boolean isRouteWhiteList(String accountNo);
}
