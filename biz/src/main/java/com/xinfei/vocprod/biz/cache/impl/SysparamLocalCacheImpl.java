///**
// * Xinfei.com Inc.
// * Copyright (c) 2004-2023 All Rights Reserved.
// */
//package com.xinfei.vocprod.biz.cache.impl;
//
//import com.xinfei.vocprod.biz.cache.enums.LocalCacheNameEnum;
//import com.xinfei.vocprod.biz.cache.SysparamLocalCache;
//import com.xinfei.xfframework.common.LoggerUtil;
//import com.xinfei.xfframework.common.starter.localcache.AbstractLocalCache;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;
//
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import static com.xinfei.vocprod.biz.cache.impl.SysparamLocalCacheImpl.CACHE_NAME;
//
///**
// * 系统参数缓存默认实现
// *
// * <AUTHOR>
// * @version $ SysparamLocalCacheImpl, v 0.1 2023/10/8 20:47 Jinyan.huang Exp $
// */
//@Component(CACHE_NAME)
//public class SysparamLocalCacheImpl extends AbstractLocalCache implements SysparamLocalCache {
//
//    /**
//     * LOGGER
//     */
//    private static final Logger LOGGER = LoggerFactory.getLogger(SysparamLocalCacheImpl.class);
//
//    /**
//     * 参数key分割符
//     */
//    private static final String ROUTE_WHITE_LIST_PARAM_KEY = "RouteWhiteList";
//
//    /**
//     * 系统参数通用Map
//     */
//    private final Map<String, String> sysparamMap = new HashMap<String, String>();
//
//    /**
//     * 系统参数Mapper
//     */
//    @Autowired
//    private SysparamMapper sysparamMapper;
//
//    @Override
//    public String fetchSystemValue(String sysCode) {
//
//        if (CollectionUtils.isEmpty(sysparamMap)) {
//            refresh(false);
//        }
//
//        return sysparamMap.get(sysCode);
//    }
//
//    @Override
//    public boolean isRouteWhiteList(String accountNo) {
//
//        String routeWhiteListStr = fetchSystemValue(ROUTE_WHITE_LIST_PARAM_KEY);
//        String[] routeWhiteListArray = StringUtils.split(routeWhiteListStr, "-");
//
//        if (routeWhiteListArray == null) {
//            return false;
//        }
//
//        List<String> routeWhiteList = Arrays.asList(routeWhiteListArray);
//
//        return routeWhiteList.contains(accountNo);
//    }
//
//    @Override
//    protected void refreshContent(boolean isEnforce) {
//
//        // 非强制刷新时，检查到缓存非空就不会执行刷新操作
//        if (!isEnforce && !CollectionUtils.isEmpty(sysparamMap)) {
//            return;
//        }
//
//        List<SysparamEntity> sysparamEntityList = sysparamMapper.selectList(null);
//
//        Map<String, String> tempSysparamMap = new HashMap<String, String>();
//        for (SysparamEntity sysparamEntity : sysparamEntityList) {
//            tempSysparamMap.put(sysparamEntity.getSysCode(), sysparamEntity.getSysValue());
//        }
//
//        sysparamMap.clear();
//        sysparamMap.putAll(tempSysparamMap);
//    }
//
//    @Override
//    public String getCacheName() {
//        return LocalCacheNameEnum.SYSTEM_PARAM.getCode();
//    }
//
//    @Override
//    public void dump() {
//
//        StringBuilder builder = new StringBuilder();
//
//        builder.append("\n=====开始：系统参数输出======\n");
//
//        for (String sysCode : sysparamMap.keySet()) {
//            builder.append(sysCode).append("=").append(sysparamMap.get(sysCode)).append("\n");
//        }
//
//        builder.append("======结束：系统参数输出=====\n");
//
//        LoggerUtil.info(LOGGER, builder.toString());
//    }
//
//    @Override
//    public boolean isLoadSuccess() {
//        return !(CollectionUtils.isEmpty(sysparamMap));
//    }
//
//}
