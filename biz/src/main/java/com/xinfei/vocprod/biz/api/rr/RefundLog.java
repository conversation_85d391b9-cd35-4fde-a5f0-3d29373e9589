/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api.rr;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ RefundLog, v 0.1 2024-10-31 20:18 junjie.yan Exp $
 */
@Data
public class RefundLog {
    @ApiModelProperty("会员卡名")
    private String cardName;
    @ApiModelProperty("会员卡id：订单id")
    private Long vipCardId;
    @ApiModelProperty("订单号")
    private String orderNumber;
    @ApiModelProperty("会员卡类型：1黑卡，2会员卡，3：飞享会员卡")
    private Integer vipCardType;
    @ApiModelProperty("退款流水号")
    private String refundFlowNumber;
    @ApiModelProperty("退款金额：分")
    private Integer amount;
    @ApiModelProperty("退款状态 0:初始化退款中，1:退款成功，2:退款失败")
    private Integer status;
    @ApiModelProperty("操作人：加密")
    private String operator;
    @ApiModelProperty("退款原因")
    private String reason;
    @ApiModelProperty("创建时间,申请退款时间")
    private String createTime;
    @ApiModelProperty("更新时间,退款成功或失败时间")
    private String updateTime;
    @ApiModelProperty("退款账号")
    private String refundAccount;
    @ApiModelProperty("退款银行")
    private String refundBankName;
    @ApiModelProperty("退款账号类型：1，银行卡，2：支付宝")
    private Integer refundAccountType;
    @ApiModelProperty("渠道")
    private String channelCode;
    @ApiModelProperty("三方渠道流水号")
    private String channelOrderNo;
    @ApiModelProperty("退款申请类型： 1：原路退回  2，线下")
    private Integer refundApplyChannel;

}