/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.log.DigestLogAnnotated;
import com.xinfei.vocprod.biz.service.PsengineService;
import com.xinfei.vocprod.facade.request.VocKeyRequest;
import com.xinfei.vocprod.facade.response.VocKeyResponse;
import com.xinfei.vocprod.itl.rr.UdeskSendMessageRequest;
import com.xinfei.xfframework.common.JsonUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 * @version $ WebHookFacadeImpl, v 0.1 2024/4/18 11:48 wancheng.qu Exp $
 */
@Api(tags = "im相关")
@RestController
@RequestMapping("/webhook")
@Slf4j
public class WebHookApi  {

    @Autowired
    private PsengineService psengineService;

    @PostMapping("/vocKeywordsSet")
    @DigestLogAnnotated("im-vocKeywordsSet")
    public VocKeyResponse vocKeywordsSet(@RequestBody VocKeyRequest req) {
        log.info("vocKeywordsSet im req:{}", JsonUtil.toJson(req));
        return psengineService.vocKeywordsSet(req);
    }

    @PostMapping("/queryPublicAccountInfo")
    @DigestLogAnnotated("im-queryPublicAccountInfo")
    public VocKeyResponse queryPublicAccountInfo(@RequestBody VocKeyRequest req) {
        log.info("queryPublicAccountInfo im req:{}", JsonUtil.toJson(req));
        return psengineService.queryPublicAccountInfo(req);
    }

    @PostMapping("/getUserStatus")
    @DigestLogAnnotated("im-getUserStatus")
    public VocKeyResponse getUserStatus(@RequestBody VocKeyRequest req) {
        log.info("getUserStatus ivr req:{}", JsonUtil.toJson(req));
        return psengineService.getUserStatus(req);
    }

    @PostMapping("/queryIVROrderList")
    @DigestLogAnnotated("im-queryIVROrderList")
    public VocKeyResponse queryIVROrderList(@RequestBody VocKeyRequest req) {
        log.info("queryIVROrderList ivr req:{}", JsonUtil.toJson(req));
        return psengineService.queryIVROrderList(req);
    }

    @PostMapping("/verifyIdCardLast6")
    @DigestLogAnnotated("im-verifyIdCardLast6")
    public VocKeyResponse verifyIdCardLast6(@RequestBody VocKeyRequest req) {
        log.info("verifyIdCardLast6 ivr req:{}", JsonUtil.toJson(req));
        return psengineService.verifyIdCardLast6(req);
    }

    @PostMapping("/queryUserVipStatus")
    @DigestLogAnnotated("im-queryUserVipStatus")
    public VocKeyResponse queryUserVipStatus(@RequestBody VocKeyRequest req) {
        log.info("queryUserVipStatus im req:{}", JsonUtil.toJson(req));
        return psengineService.queryUserVipStatus(req);
    }

    @PostMapping("/queryVipClassifyGroup")
    @DigestLogAnnotated("im-queryVipClassifyGroup")
    public VocKeyResponse queryVipClassifyGroup(@RequestBody VocKeyRequest req) {
        log.info("queryVipClassifyGroup im req:{}", JsonUtil.toJson(req));
        return psengineService.queryVipClassifyGroup(req);
    }

    @PostMapping("/queryIVRLatestBill")
    @DigestLogAnnotated("im-queryIVRLatestBill")
    public VocKeyResponse queryIVRLatestBill(@RequestBody VocKeyRequest req) {
        log.info("queryIVRLatestBill ivr req:{}", JsonUtil.toJson(req));
        return psengineService.queryIVRLatestBill(req);
    }

    @PostMapping("/queryIVRMonthlyAmount")
    @DigestLogAnnotated("im-queryIVRMonthlyAmount")
    public VocKeyResponse queryIVRMonthlyAmount(@RequestBody VocKeyRequest req) {
        log.info("queryIVRMonthlyAmount ivr req:{}", JsonUtil.toJson(req));
        return psengineService.queryIVRMonthlyAmount(req);
    }

    @PostMapping("/queryIVRLoanSettlement")
    @DigestLogAnnotated("im-queryIVRLoanSettlement")
    public VocKeyResponse queryIVRLoanSettlement(@RequestBody VocKeyRequest req) {
        log.info("queryIVRLoanSettlement ivr req:{}", JsonUtil.toJson(req));
        return psengineService.queryIVRLoanSettlement(req);
    }

    @PostMapping("/sendSms")
    @DigestLogAnnotated("im-verifyIdCardLast6")
    public void sendSms(@RequestBody UdeskSendMessageRequest req) {
        log.info("sendSms req:{}", JsonUtil.toJson(req));
        psengineService.sendSms(req);
    }

    @PostMapping("/queryMembershipCards")
    @DigestLogAnnotated("im-queryMembershipCards")
    public VocKeyResponse queryMembershipCards(@RequestBody VocKeyRequest req) {
        log.info("queryMembershipCards req:{}", JsonUtil.toJson(req));
        return psengineService.queryMembershipCards(req);
    }

    @PostMapping("/queryUserBillDetail")
    @DigestLogAnnotated("im-queryUserBillDetail")
    public VocKeyResponse queryUserBillDetail(@RequestBody VocKeyRequest req) {
        log.info("queryUserBillDetail req:{}", JsonUtil.toJson(req));
        return psengineService.queryUserBillDetail(req);
    }

    @PostMapping("/queryUserABGroup")
    @DigestLogAnnotated("im-queryUserABGroup")
    public VocKeyResponse queryUserABGroup(@RequestBody VocKeyRequest req) {
        log.info("queryUserABGroup req:{}", JsonUtil.toJson(req));
        return psengineService.queryUserABGroup(req);
    }
}