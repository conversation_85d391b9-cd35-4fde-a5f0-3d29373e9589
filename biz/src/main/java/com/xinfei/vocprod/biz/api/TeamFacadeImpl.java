/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.api;

import com.xinfei.vocprod.biz.common.TechplayConstants;
import com.xinfei.vocprod.biz.log.DigestLogAnnotated;
import com.xinfei.vocprod.biz.log.QueryDigestLog;
import com.xinfei.vocprod.biz.service.TeamService;
import com.xinfei.vocprod.biz.template.QueryCallBack;
import com.xinfei.vocprod.biz.template.QueryTemplate;
import com.xinfei.vocprod.biz.util.ParamCheckUtil;
import com.xinfei.vocprod.facade.TeamFacade;
import com.xinfei.vocprod.facade.rr.TeamQueryRequest;
import com.xinfei.vocprod.facade.rr.TeamQueryResponse;
import com.xinfei.vocprod.facade.rr.dto.TeamDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xinfei.vocprod.biz.util.ParamCheckUtil.TEAM_CODE;

/**
 * 团队信息远程接口默认实现类<br/>
 *
 * <AUTHOR>
 * @version $ TeamFacadeImpl, v 0.1 2023/8/28 12:20 Jinyan.Huang Exp $
 */
@RestController
@RequestMapping("/team")
public class TeamFacadeImpl implements TeamFacade {

    /** 团队服务 */
    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired
    private TeamService teamService;

    @Override
    @PostMapping("/querybycode")
    @DigestLogAnnotated(TechplayConstants.QUERY_DIGEST_LOG)
    public TeamQueryResponse queryByCode(@RequestBody TeamQueryRequest request) {

        final TeamQueryResponse result = new TeamQueryResponse();

        QueryTemplate.query(result, new QueryCallBack() {
            public void checkParameter() {
                ParamCheckUtil.checkParamNotNull(request, TEAM_CODE);
                ParamCheckUtil.checkParamNotBlank(request.getTeamCode(), TEAM_CODE);
            }

            public void doQuery() {
                TeamDto teamDto = teamService.query(request.getTeamCode());
                result.setData(teamDto);
            }

            public QueryDigestLog buildDigestLog() {
                return new QueryDigestLog(request);
            }

        });

        return result;
    }

}
