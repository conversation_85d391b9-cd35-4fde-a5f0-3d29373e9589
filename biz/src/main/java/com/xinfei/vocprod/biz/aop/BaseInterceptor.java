/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.aop;

import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

import java.lang.reflect.Method;

/**
 * 拦截器基类
 *
 * <AUTHOR>
 * @version $ BaseInterceptor, v 0.1 2023/8/28 16:16 <PERSON><PERSON>.Huang Exp $
 */
public abstract class BaseInterceptor implements MethodInterceptor {

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {

        //排除原生方法
        Method[] methods = Object.class.getMethods();

        for (Method m : methods) {
            if (invocation.getMethod().equals(m)) {
                return null;
            }
        }

        return businessInvoke(invocation);
    }

    /**
     * 业务拦截处理方法
     *
     * @param invocation 调用方法
     * @return 调用结果
     * @throws Throwable 运行时异常
     */
    public abstract Object businessInvoke(MethodInvocation invocation) throws Throwable;
}
