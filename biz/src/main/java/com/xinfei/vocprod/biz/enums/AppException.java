/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.enums;

import lombok.Getter;

/**
 * Techplay异常基类
 *
 * <AUTHOR>
 * @version $ TechplayException, v 0.1 2023/8/28 18:44 <PERSON><PERSON>.Huang Exp $
 */
@Getter
public class AppException extends RuntimeException{

    /** 结果枚举
     * -- GETTER --
     *  Getter method for property <tt>resultCodeEnum</tt>.
     *
     * @return property value of resultCodeEnum
     */
    private final AppErrDtlEnum resultCodeEnum;

    /** 额外的异常信息
     * -- GETTER --
     *  Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg
     */
    private String                     msg;

    /**
     * 构造函数
     *
     * @param resultCodeEnum    错误描述枚举
     */
    public AppException(AppErrDtlEnum resultCodeEnum) {
        this.resultCodeEnum = resultCodeEnum;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum    错误明细码枚举
     * @param msg               额外的信息，用于打印到日志中方便查找问题
     */
    public AppException(AppErrDtlEnum resultCodeEnum, String msg) {
        super(msg);
        this.resultCodeEnum = resultCodeEnum;
        this.msg = msg;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum    错误明细码枚举
     * @param cause             异常
     */
    public AppException(AppErrDtlEnum resultCodeEnum, Throwable cause) {
        super(cause);
        this.resultCodeEnum = resultCodeEnum;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum    错误明细码枚举
     * @param msg               额外的信息，用于打印到日志中方便查找问题
     * @param cause             异常
     */
    public AppException(AppErrDtlEnum resultCodeEnum, String msg, Throwable cause) {
        super(msg, cause);
        this.resultCodeEnum = resultCodeEnum;
        this.msg = msg;
    }

    /**
     * @see Throwable#getMessage()
     */
    @Override
    public String getMessage() {
        StringBuilder sb = new StringBuilder(200);
        if (super.getMessage() != null) {
            sb.append(super.getMessage());
        }
        return sb.toString();
        /*
        StringBuilder sb = new StringBuilder(200);
        if (super.getMessage() != null) {
            sb.append(super.getMessage());
        }
        sb.append(" 异常原因：");
        sb.append(resultCodeEnum.getDescription());
        if (StringUtils.hasText(msg)) {
            sb.append("|");
            sb.append(msg);
        }
        return sb.toString();
        */
    }

    //~~~ 属性方法 ~~~

}
