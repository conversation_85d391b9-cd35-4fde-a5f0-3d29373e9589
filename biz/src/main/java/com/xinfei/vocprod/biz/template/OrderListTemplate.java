/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import java.util.Map;

/**
 * 订单列表HTML模板
 *
 * <AUTHOR>
 * @version $ OrderListTemplate, v 0.1 2025/1/2 14:30 shaohui.chen Exp $
 */
public class OrderListTemplate implements HtmlTemplate {

    @Override
    public String render(Object data, Map<String, Object> config) {
        // TODO: 实现订单列表模板
        return "<div>订单列表模板待实现</div>";
    }

    @Override
    public String getTemplateName() {
        return "订单列表";
    }

    @Override
    public Class<?> getSupportedDataType() {
        return Object.class;
    }
}
