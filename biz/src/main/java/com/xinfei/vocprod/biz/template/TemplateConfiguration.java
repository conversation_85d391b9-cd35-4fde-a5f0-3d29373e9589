/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocprod.biz.template;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 模板配置类
 * 用于配置和注册各种HTML模板
 *
 * <AUTHOR>
 * @version $ TemplateConfiguration, v 0.1 2025/1/2 14:35 shaohui.chen Exp $
 */
@Configuration
public class TemplateConfiguration {

    /**
     * 配置账单时间轴模板
     */
    @Bean
    public BillTimelineTemplate billTimelineTemplate() {
        return new BillTimelineTemplate();
    }

    /**
     * 配置会员卡列表模板
     */
    @Bean
    public MembershipCardListTemplate membershipCardListTemplate() {
        return new MembershipCardListTemplate();
    }

    /**
     * 配置订单列表模板
     */
    @Bean
    public OrderListTemplate orderListTemplate() {
        return new OrderListTemplate();
    }

    /**
     * 配置支付记录模板
     */
    @Bean
    public PaymentRecordTemplate paymentRecordTemplate() {
        return new PaymentRecordTemplate();
    }

    /**
     * 配置借款汇总模板
     */
    @Bean
    public LoanSummaryTemplate loanSummaryTemplate() {
        return new LoanSummaryTemplate();
    }

    /**
     * 配置退款历史模板
     */
    @Bean
    public RefundHistoryTemplate refundHistoryTemplate() {
        return new RefundHistoryTemplate();
    }
}
