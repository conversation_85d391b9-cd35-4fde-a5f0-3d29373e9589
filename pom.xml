<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xinfei.springboot</groupId>
        <artifactId>springboot-parent</artifactId>
        <version>1.0.9.20240226</version>
    </parent>
    <groupId>com.xinfei.vocprod</groupId>
    <artifactId>vocprod</artifactId>
    <version>1.0.4.20250523-SNAPSHOT</version>
    <packaging>pom</packaging>
    <properties>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <xfframework.version>1.4.9.20250611</xfframework.version>
        <xfframework.mq.version>1.4.9.20250612</xfframework.mq.version>
        <psenginecore.version>1.0.0.20240416.1</psenginecore.version>
        <cashiercore.version>1.0.1.20240830</cashiercore.version>
        <xinfei.common.version>1.0.0.20240621</xinfei.common.version>
        <vip-core.version>1.0.10.20250617</vip-core.version>
        <cis-query-facade.version>20250401.2.RELEASE</cis-query-facade.version>
        <user-auth-core-facade.version>20250401.4.RELEASE</user-auth-core-facade.version>
        <cis-ext-info-facade.version>20250401.2.RELEASE</cis-ext-info-facade.version>
        <supervip-interfaces.version>1.0.0.20250617</supervip-interfaces.version>
        <supervip-common.version>1.0.0.20250617</supervip-common.version>
        <cisaggs-facade.version>20250522.2.RELEASE</cisaggs-facade.version>
        <arms.apm.version>1.7.3</arms.apm.version>
        <net.logstash.logback.version>6.6</net.logstash.logback.version>
        <!-- 集成测试，跑执行测试的时候通过 -D 进行参数设置 -->
        <isSkipUnitTest>true</isSkipUnitTest>
        <isSkipIntegrationTest>true</isSkipIntegrationTest>
        <skipTests>true</skipTests>
    </properties>
    <modules>
        <module>dal</module>
        <module>facade</module>
        <module>biz</module>
        <module>bootstrap</module>
        <module>itl</module>
        <module>util</module>
        <module>test</module>

    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xinfei.xfframework</groupId>
                <artifactId>xfframework-dependency</artifactId>
                <version>${xfframework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.xfframework</groupId>
                <artifactId>xfframework-dependency-pom</artifactId>
                <version>${xfframework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.xinfei.xfframework</groupId>
                <artifactId>xfframework-starter-mq</artifactId>
                <version>${xfframework.mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.vocprod</groupId>
                <artifactId>vocprod-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.vocprod</groupId>
                <artifactId>vocprod-biz</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.vocprod</groupId>
                <artifactId>vocprod-itl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.vocprod</groupId>
                <artifactId>vocprod-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.vocprod</groupId>
                <artifactId>vocprod-util</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.psenginecore</groupId>
                <artifactId>psenginecore-facade</artifactId>
                <version>${psenginecore.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.cashiercore</groupId>
                <artifactId>cashiercore-common-service-facade</artifactId>
                <version>${cashiercore.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.common</groupId>
                <artifactId>xinfei-common-lang</artifactId>
                <version>${xinfei.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.vipcore</groupId>
                <artifactId>vipcore-facade</artifactId>
                <version>${vip-core.version}</version>
            </dependency>
            <!--cis查询相关-->
            <dependency>
                <groupId>com.xyf.user</groupId>
                <artifactId>cis-query-facade</artifactId>
                <version>${cis-query-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xyf.user</groupId>
                <artifactId>user-auth-core-facade</artifactId>
                <version>${user-auth-core-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xyf.user</groupId>
                <artifactId>cis-ext-info-facade</artifactId>
                <version>${cis-ext-info-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.cisaggs</groupId>
                <artifactId>cisaggs-facade</artifactId>
                <version>${cisaggs-facade.version}</version>
            </dependency>
            <dependency>
                <artifactId>cis-secure-client</artifactId>
                <groupId>com.xyf.user</groupId>
                <version>20240308</version>
            </dependency>
            <!--lcs-->
            <dependency>
                <groupId>io.kyoto.pillar</groupId>
                <artifactId>lcs-api</artifactId>
                <version>1.0.27-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.kyoto</groupId>
                        <artifactId>sole-api</artifactId>
                    </exclusion>
                    <!-- 项目使用log4j2日志，去除springboot默认日志依赖 -->
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
