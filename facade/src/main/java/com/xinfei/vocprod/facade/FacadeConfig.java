/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.facade;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 接口配置config
 *
 * <AUTHOR>
 * @version $ FacadeConfig, v 0.1 2023/8/28 21:55 <PERSON>yan.Huang Exp $
 */
@Configuration
@EnableFeignClients(basePackageClasses = {FacadeConfig.class})
@ComponentScan(basePackageClasses = {FacadeConfig.class})
public class FacadeConfig {
}
