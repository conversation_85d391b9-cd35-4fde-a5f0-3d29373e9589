/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.facade;

import com.xinfei.vocprod.facade.rr.ScreenshotConfineRequest;
import com.xinfei.vocprod.facade.rr.ScreenshotConfineResponse;
import com.xinfei.vocprod.facade.rr.UserRequest;
import com.xinfei.vocprod.facade.rr.UserResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 用户信息远程接口<br/>
 *
 * <br/><br/>包含如下方法：
 * <li> 根据用户信息创建或者编辑用户
 * <br/><br/><br/>服务配置说明：使用了信飞的xfframework，框架会自动生成Feign的配置信息。详情参考源码。
 * <li>feign配置：com.xinfei.xfframework.common.starter.feign.XfServiceInstance.createUri()
 * <AUTHOR>
 * @version $ UserFacade, v 0.1 2023/8/28 11:30 Chengsheng.Li Exp $
 */
@FeignClient(name = Constants.SERVICE_NAME,contextId= Constants.SERVICE_NAME+".UserFacade",path ="/user" )
public interface UserFacade {

    /**
     * 根据用户信息创建或者编辑用户
     *
     * @param   request 用户request，必填，非空
     * @return  添加或者编辑结果
     */
    @PostMapping("/addOrEdit")
    UserResponse addOrEdit(UserRequest request);

    /**
     * 截屏限制查询
     *
     * @param   request 用户request，必填，非空
     * @return  添加或者编辑结果
     */
    @PostMapping("/getScreenshotConfine")
    ScreenshotConfineResponse getScreenshotConfine(ScreenshotConfineRequest request);
}
