/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.facade;

import com.xinfei.vocprod.facade.rr.UDeskRequest;
import com.xinfei.vocprod.facade.rr.UDeskResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;


@FeignClient(name = Constants.SERVICE_NAME,contextId= Constants.SERVICE_NAME+".UDeskFacade",path ="/udesk" )
public interface UDeskFacade {

    @PostMapping("/getRsaUrl")
    UDeskResponse getRsaUrl(UDeskRequest request);
}
