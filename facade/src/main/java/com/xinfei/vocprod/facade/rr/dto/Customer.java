/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.facade.rr.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonRootName;
import lombok.*;

/**
 *
 * <AUTHOR>
 * @version $ Customer, v 0.1 2024/7/8 13:58 wancheng.qu Exp $
 */

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonRootName("customer")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Customer {
    private String c_name;
    private String c_email;
    private String c_phone;
    private String c_desc;
    private String c_tags;
    private String c_vip;
    private String web_token;
    private String c_cf_uid;
    private String c_cf_dialogueDesc;
    private String c_cf_会员卡;
    private String c_cf_会员卡分流;
}