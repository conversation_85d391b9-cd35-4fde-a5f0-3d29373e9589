/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.facade.rr.dto;

import com.xinfei.xfframework.common.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户信息DTO
 *
 * <AUTHOR>
 * @version $ TeamQueryRequest, v 0.1 2023/8/29 09:54 Chengsheng.Li Exp $
 */
@Data
public class UserDto extends BaseDto {
    @ApiModelProperty(value = "用户名称")
    private String name;
    @ApiModelProperty(value = "用户年龄")
    private Integer age;
    @ApiModelProperty(value = "用户email")
    private String email;

    @ApiModelProperty(value = "用户Id")
    private Long id;
}
