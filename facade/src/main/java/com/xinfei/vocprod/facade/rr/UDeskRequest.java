/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.facade.rr;

import com.xinfei.vocprod.facade.rr.dto.Customer;
import com.xinfei.xfframework.common.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户信息请求类
 *
 * <AUTHOR>
 * @version $ UserRequest, v 0.1 2023/8/29 09:54 Chengsheng.Li Exp $
 */
@Data
public class UDeskRequest extends BaseRequest {
    @ApiModelProperty(value = "traceId")
    private String traceId;
    @ApiModelProperty(value = "https://[这里替换为客服系统域名]/im_client/?web_plugin_id=[替换为网页插件id]")
    private String urlBase;
    @ApiModelProperty(value = "网页插件的public key")
    private String upbKey;
    @ApiModelProperty(value = "网页插件用于身份认证的 key")
    private String imUserKey;
    @ApiModelProperty(value = "链接拼接参数")
    private Customer customer;
}
