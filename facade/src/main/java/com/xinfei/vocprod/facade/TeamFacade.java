/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.facade;

import com.xinfei.vocprod.facade.rr.TeamQueryRequest;
import com.xinfei.vocprod.facade.rr.TeamQueryResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 团队信息远程接口<br/>
 *
 * <br/><br/>包含如下方法：
 * <li> 根据团队编码获取团队当前的总人数
 * <br/><br/><br/>服务配置说明：使用了信飞的xfframework，框架会自动生成Feign的配置信息。详情参考源码。
 * <li>feign配置：com.xinfei.xfframework.common.starter.feign.XfServiceInstance.createUri()
 * <AUTHOR>
 * @version $ TeamFacade, v 0.1 2023/8/28 11:30 <PERSON><PERSON>.Huang Exp $
 */
@FeignClient(name = Constants.SERVICE_NAME,contextId= Constants.SERVICE_NAME+".TeamFacade",path ="/team" )
public interface TeamFacade {

    /**
     * 根据组织代码查询组织信息
     *
     * @param   teamQueryRequest 组织代码request，必填，非空
     * @return  查询结果
     */
    @PostMapping("/querybycode")
    TeamQueryResponse queryByCode(TeamQueryRequest teamQueryRequest);
}
