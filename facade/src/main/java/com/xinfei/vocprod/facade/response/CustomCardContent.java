/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.facade.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 自定义卡片内容
 *
 * <AUTHOR>
 * @version $ CustomCardContent, v 0.1 2024-12-20 AI Exp $
 */
@Data
public class CustomCardContent {

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("是否轮播标志 0否1是")
    private Integer turnFlag;

    @ApiModelProperty("卡片列表显示数量")
    private Integer showSize;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("副标题")
    private String subTitle;

    @ApiModelProperty("卡片列表")
    private List<CardItem> cardList;

    @Data
    public static class CardItem {
        @ApiModelProperty("卡片Id")
        private String id;

        @ApiModelProperty("卡片类型 1.富文本卡片 2.FAQ推荐列表卡片")
        private Integer type;

        @ApiModelProperty("卡片是否能点击 0否 1是")
        private Integer isHit;

        @ApiModelProperty("卡片点击效果 1超链接 2回复消息 3结构化消息 4小程序链接")
        private Integer hitType;

        @ApiModelProperty("卡片点击内容")
        private String hitContent;

        @ApiModelProperty("点击消息发送的消息卡片")
        private String hitShowContent;

        @ApiModelProperty("富文本具体内容")
        private String content;

        @ApiModelProperty("FAQ推荐卡片的文案")
        private String suggestContent;

        @ApiModelProperty("FAQ推荐的问题列表")
        private List<SuggestItem> suggestList;
    }

    @Data
    public static class SuggestItem {
        @ApiModelProperty("问题的标识id")
        private Integer id;

        @ApiModelProperty("问题的内容")
        private String content;

        @ApiModelProperty("问题类型")
        private Integer type;
    }
}
