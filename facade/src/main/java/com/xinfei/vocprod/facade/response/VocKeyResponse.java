/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.facade.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 *  出入参文档地址  https://www.udesk.cn/doc/robot/webhook/#msg_type
 * <AUTHOR>   im第三方固定要求返回类型,不能
 * @version $ VocKeyResponse, v 0.1 2024/4/18 11:38 wancheng.qu Exp $
 */
@Data
public class VocKeyResponse implements Serializable {

    @ApiModelProperty(value = "响应码【0成功/-1失败】")
    private Integer resCode;
    @ApiModelProperty(value = "非用户级别错误信息")
    private String resError;
    @ApiModelProperty(value = "用户消息")
    private Message	message;
    @ApiModelProperty(value = "变量设置,你可以修改变量表")
    private Map<String,Object> variables;
    @ApiModelProperty(value = "跳转任务节点ID,动作是跳转节点有效")
    private Integer toTaskNodeId;
    @ApiModelProperty(value = "跳转节点类型（1.直接触发 2.等待回复）")
    private Integer routeType;
    @ApiModelProperty(value = "动作指令")
    private String command;

    @Data
    public static class Message {
        @ApiModelProperty(value = "消息类型")
        private String msgType="plain";
        @ApiModelProperty(value = "消息内容")
        private Object msgContent="";

    }
}