/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.facade.request;

import com.xinfei.xfframework.common.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ VocKeyRequest, v 0.1 2024/4/18 11:24 wancheng.qu Exp $
 */
@Data
public class VocKeyRequest  extends BaseRequest {

    @ApiModelProperty(value = "节点ID,当前任务式会话匹配停留的节点Id（当在任务树里使用时必填，在新表单里使用时为空")
    private Integer taskNodeId;
    @ApiModelProperty(value = "会话ID,对话记录的Id")
    private Integer sessionId;
    @ApiModelProperty(value = "时间戳")
    private Long timestamp;
    @ApiModelProperty(value = "签名")
    private String sign;
    @ApiModelProperty(value = "变量Map,任务树配置里搜集到的变量")
    private Map<String, Object> variables;
    @ApiModelProperty(value = "客户自定义附加对象")
    private CustomerExtra customerExtra;


    @Data
    public static class CustomerExtra {
        @ApiModelProperty(value = "客服系统的用户Id")
        private Long customerId;
        @ApiModelProperty(value = "语音机器人主叫号码（此字段为语音机器人使用")
        private String callerNumber;
        @ApiModelProperty(value = "此字段使用比较特殊，是为了满足客户给机器人对话的Webhook传自定义信息的需求 首先要在客服系统里，客户自定义字段内，设置一个名字叫做c_cf_dialogueDesc的自定义字段 可以设置客户的该字段值 之后打开聊天插件,将该字段添加到路径参数里，格式如下 https://xxx.udesk.cn/im_client/?web_plugin_id=41597&c_cf_dialogueDesc=* 这样系统会自动将该参数值以参数dialogueDesc的形式当做webhook的一个请求参数传进去 特殊提醒：客服系统和路径传参都是以c_cf_dialogueDesc参数名进行传值")
        private String dialogueDesc;
        @ApiModelProperty(value = "查询三方电话账单")
        private String otherCallerNumber;

    }


}