package com.xinfei.vocprod.util.util;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.alibaba.fastjson.JSON;
import org.slf4j.helpers.MessageFormatter;

/**
 * 日志格式化
 * <AUTHOR>
 * @version $ ArgumentJsonFormatLayout, v 0.1 2025/3/24 15:58 shaohui.chen Exp $
 */
public class ArgumentJsonFormatLayout extends MessageConverter {
    public ArgumentJsonFormatLayout() {
    }

    public String convert(ILoggingEvent event) {
        try {
            Object[] argumentArray = event.getArgumentArray();
            if (argumentArray != null && argumentArray.length != 0) {
                Object[] formatArgumentArray = new Object[argumentArray.length];

                for(int i = 0; i < argumentArray.length; ++i) {
                    Object argument = argumentArray[i];
                    if (argument != null) {
                        if (argument instanceof String) {
                            formatArgumentArray[i] = argument;
                        } else if (argument instanceof Number) {
                            formatArgumentArray[i] = argument.toString();
                        } else {
                            formatArgumentArray[i] = JSON.toJSONString(argument);
                        }
                    }
                }

                return MessageFormatter.arrayFormat(event.getMessage(), formatArgumentArray).getMessage();
            } else {
                return event.getFormattedMessage();
            }
        } catch (Exception exception) {
            return event.getFormattedMessage();
        }
    }
}
