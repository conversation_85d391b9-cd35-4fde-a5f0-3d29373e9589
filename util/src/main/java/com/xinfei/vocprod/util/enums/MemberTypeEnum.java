/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocprod.util.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员类型枚举
 *
 * <AUTHOR>
 * @version $ MemberTypeEnum, v 0.1 2025/05/10 10:00 chenshaohui Exp $
 */
@Getter
@AllArgsConstructor
public enum MemberTypeEnum {

    /**
     * 非会员
     */
    NON_MEMBER("non_member", "", "非会员"),

    /**
     * 飞跃会员
     */
    FEI_YUE("fei_yue", "1", "飞跃会员"),

    /**
     * 飞享会员
     */
    FEI_XIANG("fei_xiang", "0", "飞享会员"),
    ;

    /**
     * 枚举代码
     */
    private final String code;

    /**
     * udesk code
     */
    private final String udeskCode;

    /**
     * 枚举描述
     */
    private final String description;


    /**
     * 根据代码值获取枚举对象
     *
     * @param code 根据枚举的代码获取枚举对象
     * @return 返回枚举对象，如果没有对应枚举对象时返回<code>null</code>
     */
    public static MemberTypeEnum getByCode(String code) {
        for (MemberTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
