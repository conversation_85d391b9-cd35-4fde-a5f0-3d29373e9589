/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.util.exception;

import com.xinfei.vocprod.util.enums.ErrDtlEnum;
import com.xinfei.xfframework.common.ErrorContext;

/**
 * 标准错误码工具
 * <p>在标准错误码的位置如下：
 * <table border="1">
 * <tr>
 * <td>位置</td><td>1</td><td>2</td><td>3</td><td bgcolor="yellow">4</td><td>5</td><td>6</td><td>7</td><td>8</td><td>9</td><td bgcolor="red">10</td><td bgcolor="red">11</td><td bgcolor="red">12</td>
 * </tr>
 * <tr>
 * <td>示例</td><td>X</td><td>E</td><td>0</td><td>1</td><td>0</td><td>1</td><td>0</td><td>1</td><td>1</td><td>0</td><td>2</td><td>7</td>
 * </tr>
 * <tr>
 * <td>说明</td><td colspan=2>固定<br>标识</td><td>规<br>范<br>版<br>本</td><td>错<br>误<br>级<br>别</td><td>错<br>误<br>类<br>型</td><td colspan=4>错误场景</td><td colspan=3>错误编<br>码</td>
 * </tr>
 * </table>
 *
 * <AUTHOR>
 * @version $ ErrorContextUtil, v 0.1 2023/8/28 18:59 Jinyan.Huang Exp $
 */
public final class ErrorContextUtil {

    /**
     * 错误码前缀，信飞全站统一
     */
    private final static String ERR_CODE_PREFIX = "XE";

    /**
     * 错误码版本，信飞全站统一
     */
    private final static String ERR_CODE_VERSION = "0";

    /**
     * 错误码类型，信飞全站统一
     */
    private final static String ERR_CODE_TYPE = "0";

    /**
     * gitlab应用编号
     */
    private final static String ACTIVITY_CODE = "1120";

    /**
     * 场景编号
     */
    private final static String SCENE_CODE = "000";


    /**
     * 生成标准错误码
     *
     * @param detail   错误明细
     * @param message  错误信息
     * @return 错误上下文
     */
    public static ErrorContext genErrorContext(ErrDtlEnum detail, String message) {

        // 信飞统一前缀：2位
        String codeStr = ERR_CODE_PREFIX;
        // 错误码版本：1位
        codeStr = codeStr + ERR_CODE_VERSION;
        // 错误码级别：1位
        codeStr = codeStr + detail.getErrorLevel().getCode();
        // 错误码类型：1位
        codeStr = codeStr + ERR_CODE_TYPE;
        // 应用编码：4位
        codeStr = codeStr + ACTIVITY_CODE;
        // 场景码：3位 暂时固定000
        codeStr = codeStr + SCENE_CODE;
        // 错误码：3位
        codeStr = codeStr + detail.getCode();

        ErrorContext errorContext = new ErrorContext();
        errorContext.setErrCode(codeStr);
        errorContext.setErrDesc(message);

        return errorContext;
    }
}
