package com.xinfei.vocprod.util.interceptor;

import com.xinfei.vocprod.util.constant.MonitorConstant;
import com.xinfei.vocprod.util.logger.MonitorLogBuilder;
import com.xinfei.vocprod.util.util.IPAddressUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version $ MonitorInterceptor, v 0.1 2025/4/9 15:20 shaohui.chen Exp $
 */
@Component
public class MonitorInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        request.setAttribute(MonitorConstant.MonitorKey.REQUEST_START_TIME, System.currentTimeMillis());
        request.setAttribute(MonitorConstant.MonitorKey.MONITOR_LOG_RESULT, true);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        Boolean result = (Boolean) request.getAttribute(MonitorConstant.MonitorKey.MONITOR_LOG_RESULT);
        result = null == result || result;
        String method = request.getMethod();
        String URI = request.getServletPath();
        String remoteIp = IPAddressUtil.getRemoteIp(request);
        Long start = (Long) request.getAttribute(MonitorConstant.MonitorKey.REQUEST_START_TIME);
        Long end = System.currentTimeMillis();
        long duration = end - start;
        MonitorLogBuilder
                .getInstance(MonitorConstant.Instance.HTTP_REQUEST, duration, result)
                .builder(MonitorConstant.BuilderKey.METHOD, method)
                .builder(MonitorConstant.BuilderKey.URI, URI)
                .builder(MonitorConstant.BuilderKey.REMOTE_IP, remoteIp)
                .print();

    }
}

