package com.xinfei.vocprod.util.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version $ Base62Util, v 0.1 2025/3/20 19:48 shaohui.chen Exp $
 */
public class Base62Util {

    // Base62 字符表，索引 0~61 分别代表 [0..9, A..Z, a..z]
    private static final char[] BASE62_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".toCharArray();
    private static final int BASE = 62;

    /**
     * 将一个long数值进行base62编码(无符号处理)，返回字符串
     */
    public static String encode(long value) {
        // 若 value=0，直接返回 "0"
        if (value == 0) {
            return "0";
        }
        StringBuilder sb = new StringBuilder();
        while (value > 0) {
            int remainder = (int) (value % BASE);
            sb.append(BASE62_CHARS[remainder]);
            value = value / BASE;
        }
        // 由于是从低位往高位append, 最后需要反转
        return sb.reverse().toString();
    }

    /**
     * 将 base62 编码后的字符串还原为 long
     */
    public static long decode(String base62) {
        if (StringUtils.isBlank(base62)) {
            throw new IllegalArgumentException("base62 string is empty");
        }
        long result = 0L;
        for (int i = 0; i < base62.length(); i++) {
            result = result * BASE + charToValue(base62.charAt(i));
        }
        return result;
    }

    private static int charToValue(char c) {
        if (c >= '0' && c <= '9') {
            return c - '0';
        } else if (c >= 'A' && c <= 'Z') {
            return c - 'A' + 10;
        } else if (c >= 'a' && c <= 'z') {
            return c - 'a' + 36;
        } else {
            throw new IllegalArgumentException("Invalid base62 character: " + c);
        }
    }
}
