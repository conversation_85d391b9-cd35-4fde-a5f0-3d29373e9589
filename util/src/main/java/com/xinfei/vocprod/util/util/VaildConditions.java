package com.xinfei.vocprod.util.util;

import com.xinfei.vocprod.util.enums.ErrDtlEnum;
import com.xinfei.vocprod.util.exception.VocprodException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ VaildConditions, v 0.1 2025/3/26 16:47 shaohui.chen Exp $
 */
public class VaildConditions {

    public static void assertNotBlank(String e, String errorMsg) {
        if (StringUtils.isBlank(e)) {
            throw new VocprodException(ErrDtlEnum.PARAM_INVALID, errorMsg);
        }
    }

    public static void assertNotNull(Object e, String errorMsg) {
        if (Objects.isNull(e)) {
            throw new VocprodException(ErrDtlEnum.PARAM_INVALID, errorMsg);
        }
    }

    public static void assertNotEmpty(Collection e, String errorMsg) {
        if (CollectionUtils.isEmpty(e)) {
            throw new VocprodException(ErrDtlEnum.PARAM_INVALID, errorMsg);
        }
    }
}
