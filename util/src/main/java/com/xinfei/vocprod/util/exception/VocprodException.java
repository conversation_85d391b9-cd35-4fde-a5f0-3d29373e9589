/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocprod.util.exception;

import com.xinfei.vocprod.util.enums.ErrDtlEnum;
import org.springframework.util.StringUtils;

/**
 * VocprodException
 *
 * <AUTHOR>
 * @version $ VocmngException, v 0.1 2023/8/28 18:44 Jinyan.Huang Exp $
 */
public class VocprodException extends RuntimeException {

    /**
     * 结果枚举
     */
    private final ErrDtlEnum resultCodeEnum;

    /**
     * 额外的异常信息
     */
    private String msg;

    /**
     * 构造函数
     *
     * @param resultCodeEnum 错误描述枚举
     */
    public VocprodException(ErrDtlEnum resultCodeEnum) {
        this.resultCodeEnum = resultCodeEnum;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum 错误明细码枚举
     * @param msg            额外的信息，用于打印到日志中方便查找问题
     */
    public VocprodException(ErrDtlEnum resultCodeEnum, String msg) {
        super(msg);
        this.resultCodeEnum = resultCodeEnum;
        this.msg = msg;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum 错误明细码枚举
     * @param cause          异常
     */
    public VocprodException(ErrDtlEnum resultCodeEnum, Throwable cause) {
        super(cause);
        this.resultCodeEnum = resultCodeEnum;
    }

    /**
     * 构造函数，包含额外信息
     *
     * @param resultCodeEnum 错误明细码枚举
     * @param msg            额外的信息，用于打印到日志中方便查找问题
     * @param cause          异常
     */
    public VocprodException(ErrDtlEnum resultCodeEnum, String msg, Throwable cause) {
        super(msg, cause);
        this.resultCodeEnum = resultCodeEnum;
        this.msg = msg;
    }

    /**
     * @see Throwable#getMessage()
     */
    @Override
    public String getMessage() {
        StringBuilder sb = new StringBuilder(200);
        sb.append(resultCodeEnum.getDescription());
        if (StringUtils.hasText(msg)) {
            sb.append("|");
            sb.append(msg);
        }
        return sb.toString();
    }

    //~~~ 属性方法 ~~~

    /**
     * Getter method for property <tt>resultCodeEnum</tt>.
     *
     * @return property value of resultCodeEnum
     */
    public ErrDtlEnum getResultCodeEnum() {
        return resultCodeEnum;
    }

    /**
     * Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg
     */
    public String getMsg() {
        return msg;
    }
}
