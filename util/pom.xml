<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xinfei.vocprod</groupId>
        <artifactId>vocprod</artifactId>
        <version>1.0.4.20250523-SNAPSHOT</version>
    </parent>

    <artifactId>vocprod-util</artifactId>

    <dependencies>
        <!--xfframework-->
        <dependency>
            <groupId>com.xinfei.xfframework</groupId>
            <artifactId>xfframework-dependency</artifactId>
        </dependency>
        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.arms.apm</groupId>
            <artifactId>arms-sdk</artifactId>
            <version>${arms.apm.version}</version>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${net.logstash.logback.version}</version>
        </dependency>
    </dependencies>

</project>
