apiVersion: apps/v1
kind: Deployment
metadata:
  name: vocprod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: vocprod
  template:
    metadata:
      labels:
        app: vocprod
    spec:
      containers:
        - name: vocprod
          image: xuyou/vocprod:20231010
          ports:
            - containerPort: 8080
          imagePullPolicy: Never
---
apiVersion: v1
kind: Service
metadata:
  name: vocprod
spec:
  selector:
    app: vocprod
  ports:
    - port: 80
      targetPort: 8080
  type: LoadBalancer
